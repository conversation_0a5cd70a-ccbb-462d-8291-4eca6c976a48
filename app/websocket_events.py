"""
WebSocket event handlers for real-time updates.
"""
from flask import request
from flask_login import current_user
from flask_socketio import emit, join_room, leave_room, disconnect
from app import socketio, db
from app.models import Tournament, Match, User, UserRole
import logging

logger = logging.getLogger(__name__)


@socketio.on('connect')
def on_connect():
    """Handle client connection."""
    if current_user.is_authenticated:
        logger.info(f'User {current_user.username} connected to WebSocket')
        emit('status', {'msg': f'Welcome {current_user.username}!'})
    else:
        logger.info('Anonymous user connected to WebSocket')
        emit('status', {'msg': 'Connected to live updates'})


@socketio.on('disconnect')
def on_disconnect():
    """Handle client disconnection."""
    if current_user.is_authenticated:
        logger.info(f'User {current_user.username} disconnected from WebSocket')
    else:
        logger.info('Anonymous user disconnected from WebSocket')


@socketio.on('join_tournament')
def on_join_tournament(data):
    """Join a tournament room for live updates."""
    tournament_id = data.get('tournament_id')
    if not tournament_id:
        emit('error', {'msg': 'Tournament ID required'})
        return
    
    tournament = Tournament.query.get(tournament_id)
    if not tournament:
        emit('error', {'msg': 'Tournament not found'})
        return
    
    room = f'tournament_{tournament_id}'
    join_room(room)
    
    if current_user.is_authenticated:
        logger.info(f'User {current_user.username} joined tournament {tournament_id} room')
        emit('status', {'msg': f'Joined {tournament.name} live updates'})
    else:
        logger.info(f'Anonymous user joined tournament {tournament_id} room')
        emit('status', {'msg': f'Joined {tournament.name} live updates'})
    
    # Send current tournament status
    emit('tournament_status', {
        'tournament_id': tournament_id,
        'status': tournament.status.value,
        'name': tournament.name,
        'registered_teams': len(tournament.teams.all())
    })


@socketio.on('leave_tournament')
def on_leave_tournament(data):
    """Leave a tournament room."""
    tournament_id = data.get('tournament_id')
    if not tournament_id:
        emit('error', {'msg': 'Tournament ID required'})
        return
    
    room = f'tournament_{tournament_id}'
    leave_room(room)
    
    if current_user.is_authenticated:
        logger.info(f'User {current_user.username} left tournament {tournament_id} room')
    else:
        logger.info(f'Anonymous user left tournament {tournament_id} room')
    
    emit('status', {'msg': 'Left tournament updates'})


@socketio.on('join_match')
def on_join_match(data):
    """Join a match room for live score updates."""
    match_id = data.get('match_id')
    if not match_id:
        emit('error', {'msg': 'Match ID required'})
        return
    
    match = Match.query.get(match_id)
    if not match:
        emit('error', {'msg': 'Match not found'})
        return
    
    room = f'match_{match_id}'
    join_room(room)
    
    if current_user.is_authenticated:
        logger.info(f'User {current_user.username} joined match {match_id} room')
    else:
        logger.info(f'Anonymous user joined match {match_id} room')
    
    # Send current match status
    emit('match_status', {
        'match_id': match_id,
        'status': match.status.value,
        'team1': match.team1.name if match.team1 else 'TBD',
        'team2': match.team2.name if match.team2 else 'TBD',
        'team1_score': match.team1_score or 0,
        'team2_score': match.team2_score or 0,
        'round': match.round_number,
        'match_number': match.match_number
    })


@socketio.on('leave_match')
def on_leave_match(data):
    """Leave a match room."""
    match_id = data.get('match_id')
    if not match_id:
        emit('error', {'msg': 'Match ID required'})
        return
    
    room = f'match_{match_id}'
    leave_room(room)
    
    if current_user.is_authenticated:
        logger.info(f'User {current_user.username} left match {match_id} room')
    else:
        logger.info(f'Anonymous user left match {match_id} room')


@socketio.on('update_live_score')
def on_update_live_score(data):
    """Handle live score updates during a match (admin/organizer only)."""
    if not current_user.is_authenticated:
        emit('error', {'msg': 'Authentication required'})
        return
    
    if current_user.role not in [UserRole.ADMIN, UserRole.ORGANIZER]:
        emit('error', {'msg': 'Insufficient permissions'})
        return
    
    match_id = data.get('match_id')
    team1_score = data.get('team1_score', 0)
    team2_score = data.get('team2_score', 0)
    
    if not match_id:
        emit('error', {'msg': 'Match ID required'})
        return
    
    match = Match.query.get(match_id)
    if not match:
        emit('error', {'msg': 'Match not found'})
        return
    
    # Check if user can update this match
    if current_user.role == UserRole.ORGANIZER and match.tournament.organizer_id != current_user.id:
        emit('error', {'msg': 'You can only update matches in your tournaments'})
        return
    
    # Broadcast live score update to all users in the match room
    room = f'match_{match_id}'
    socketio.emit('live_score_update', {
        'match_id': match_id,
        'team1': match.team1.name if match.team1 else 'TBD',
        'team2': match.team2.name if match.team2 else 'TBD',
        'team1_score': team1_score,
        'team2_score': team2_score,
        'timestamp': data.get('timestamp'),
        'updated_by': current_user.username
    }, room=room)
    
    logger.info(f'Live score update for match {match_id}: {team1_score}-{team2_score} by {current_user.username}')


@socketio.on('request_tournament_update')
def on_request_tournament_update(data):
    """Request current tournament status."""
    tournament_id = data.get('tournament_id')
    if not tournament_id:
        emit('error', {'msg': 'Tournament ID required'})
        return
    
    tournament = Tournament.query.get(tournament_id)
    if not tournament:
        emit('error', {'msg': 'Tournament not found'})
        return
    
    # Get active matches
    active_matches = Match.query.filter_by(
        tournament_id=tournament_id,
        status='in_progress'
    ).all()
    
    matches_data = []
    for match in active_matches:
        matches_data.append({
            'id': match.id,
            'team1': match.team1.name if match.team1 else 'TBD',
            'team2': match.team2.name if match.team2 else 'TBD',
            'team1_score': match.team1_score or 0,
            'team2_score': match.team2_score or 0,
            'round': match.round_number,
            'match_number': match.match_number
        })
    
    emit('tournament_update', {
        'tournament_id': tournament_id,
        'status': tournament.status.value,
        'active_matches': matches_data,
        'registered_teams': len(tournament.teams.all())
    })


# Helper functions for broadcasting updates from other parts of the application

def broadcast_tournament_update(tournament_id, update_type, data):
    """Broadcast tournament updates to all connected clients."""
    room = f'tournament_{tournament_id}'
    socketio.emit('tournament_notification', {
        'type': update_type,
        'tournament_id': tournament_id,
        'data': data
    }, room=room)


def broadcast_match_update(match_id, update_type, data):
    """Broadcast match updates to all connected clients."""
    room = f'match_{match_id}'
    socketio.emit('match_notification', {
        'type': update_type,
        'match_id': match_id,
        'data': data
    }, room=room)


def broadcast_match_result(match):
    """Broadcast match result to tournament and match rooms."""
    # Broadcast to match room
    match_room = f'match_{match.id}'
    socketio.emit('match_completed', {
        'match_id': match.id,
        'team1': match.team1.name if match.team1 else 'TBD',
        'team2': match.team2.name if match.team2 else 'TBD',
        'team1_score': match.team1_score,
        'team2_score': match.team2_score,
        'winner': match.winner.name if match.winner else 'TBD',
        'completed_at': match.completed_at.isoformat() if match.completed_at else None
    }, room=match_room)
    
    # Broadcast to tournament room
    tournament_room = f'tournament_{match.tournament_id}'
    socketio.emit('tournament_match_completed', {
        'tournament_id': match.tournament_id,
        'match_id': match.id,
        'round': match.round_number,
        'match_number': match.match_number,
        'result': f"{match.team1.name if match.team1 else 'TBD'} {match.team1_score} - {match.team2_score} {match.team2.name if match.team2 else 'TBD'}"
    }, room=tournament_room)


def broadcast_tournament_started(tournament):
    """Broadcast tournament start notification."""
    room = f'tournament_{tournament.id}'
    socketio.emit('tournament_started', {
        'tournament_id': tournament.id,
        'name': tournament.name,
        'message': f'{tournament.name} has officially started!'
    }, room=room)


def broadcast_new_match_scheduled(match):
    """Broadcast new match scheduled notification."""
    tournament_room = f'tournament_{match.tournament_id}'
    socketio.emit('new_match_scheduled', {
        'tournament_id': match.tournament_id,
        'match_id': match.id,
        'team1': match.team1.name if match.team1 else 'TBD',
        'team2': match.team2.name if match.team2 else 'TBD',
        'round': match.round_number,
        'match_number': match.match_number,
        'scheduled_time': match.scheduled_time.isoformat() if match.scheduled_time else None
    }, room=tournament_room)
