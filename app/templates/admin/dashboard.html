{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('admin.analytics') }}" class="btn btn-outline-primary">
                    <i class="fas fa-chart-line"></i> Analytics
                </a>
                <a href="{{ url_for('admin.user_management') }}" class="btn btn-outline-success">
                    <i class="fas fa-users"></i> Users
                </a>
                <a href="{{ url_for('admin.system_health') }}" class="btn btn-outline-warning">
                    <i class="fas fa-heartbeat"></i> Health
                </a>
                <a href="{{ url_for('admin.reports') }}" class="btn btn-outline-info">
                    <i class="fas fa-file-alt"></i> Reports
                </a>
                <button type="button" class="btn btn-outline-secondary" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Overview -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Tournaments
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_tournaments }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-trophy fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active_users }}</div>
                        <div class="small text-muted">
                            {% if growth_metrics.new_users_growth > 0 %}
                            <i class="fas fa-arrow-up text-success"></i> +{{ growth_metrics.new_users_growth }}% this month
                            {% elif growth_metrics.new_users_growth < 0 %}
                            <i class="fas fa-arrow-down text-danger"></i> {{ growth_metrics.new_users_growth }}% this month
                            {% else %}
                            <i class="fas fa-minus text-muted"></i> No change this month
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Matches
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_matches }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-gamepad fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Registered Teams
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_teams }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users-cog fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics Row -->
<div class="row mb-4">
    <div class="col-xl-2 col-md-4 mb-3">
        <div class="card stats-card border-left-success">
            <div class="card-body">
                <div class="text-center">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Tournaments</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">{{ stats.active_tournaments }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 mb-3">
        <div class="card stats-card border-left-primary">
            <div class="card-body">
                <div class="text-center">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Completed Matches</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">{{ stats.completed_matches }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 mb-3">
        <div class="card stats-card border-left-danger">
            <div class="card-body">
                <div class="text-center">
                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Live Streams</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">{{ stats.active_streams }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-md-12">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">System Health</div>
                        <div class="row">
                            <div class="col-4">
                                <small class="text-muted">CPU</small>
                                <div class="progress progress-sm">
                                    <div class="progress-bar {% if system_health.cpu_usage < 70 %}bg-success{% elif system_health.cpu_usage < 85 %}bg-warning{% else %}bg-danger{% endif %}"
                                         style="width: {{ system_health.cpu_usage }}%"></div>
                                </div>
                                <small>{{ system_health.cpu_usage }}%</small>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">Memory</small>
                                <div class="progress progress-sm">
                                    <div class="progress-bar {% if system_health.memory_usage < 70 %}bg-success{% elif system_health.memory_usage < 85 %}bg-warning{% else %}bg-danger{% endif %}"
                                         style="width: {{ system_health.memory_usage }}%"></div>
                                </div>
                                <small>{{ system_health.memory_usage }}%</small>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">Disk</small>
                                <div class="progress progress-sm">
                                    <div class="progress-bar {% if system_health.disk_usage < 70 %}bg-success{% elif system_health.disk_usage < 85 %}bg-warning{% else %}bg-danger{% endif %}"
                                         style="width: {{ system_health.disk_usage }}%"></div>
                                </div>
                                <small>{{ system_health.disk_usage }}%</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-server fa-2x {% if system_health.status == 'healthy' %}text-success{% elif system_health.status == 'warning' %}text-warning{% else %}text-danger{% endif %}"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Tournament Activity Overview</h6>
            </div>
            <div class="card-body">
                <canvas id="tournamentChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Tournament Status Distribution</h6>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Recent Tournaments</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Tournament</th>
                                <th>Status</th>
                                <th>Teams</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for tournament in recent_tournaments %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-trophy text-warning me-2"></i>
                                        <div>
                                            <div class="font-weight-bold">{{ tournament.name }}</div>
                                            <div class="text-muted small">{{ tournament.game.title() }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if tournament.status.value == 'registration_open' else 'secondary' }}">
                                        {{ tournament.status.value.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>{{ tournament.teams|length }}/{{ tournament.max_teams }}</td>
                                <td>{{ tournament.created_at.strftime('%m/%d/%Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('tournaments.view', id=tournament.id) }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('tournaments.edit', id=tournament.id) }}" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">System Activity</h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    {% for activity in recent_activities %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-{{ activity.type }}"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">{{ activity.title }}</h6>
                            <p class="timeline-text">{{ activity.description }}</p>
                            <small class="text-muted">{{ activity.timestamp.strftime('%m/%d/%Y %H:%M') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Settings Modal -->
<div class="modal fade" id="systemSettingsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">System Settings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="systemSettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Site Name</label>
                                <input type="text" class="form-control" name="site_name" value="Esports Tournament Manager">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Max Teams per Tournament</label>
                                <input type="number" class="form-control" name="max_teams" value="32">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Default Tournament Format</label>
                                <select class="form-control" name="default_format">
                                    <option value="single_elimination">Single Elimination</option>
                                    <option value="double_elimination">Double Elimination</option>
                                    <option value="round_robin">Round Robin</option>
                                    <option value="swiss">Swiss</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Registration Duration (days)</label>
                                <input type="number" class="form-control" name="registration_duration" value="7">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="auto_bracket_generation" checked>
                            <label class="form-check-label">
                                Auto-generate brackets when registration closes
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="email_notifications" checked>
                            <label class="form-check-label">
                                Send email notifications to participants
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveSystemSettings()">Save Settings</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.border-left-primary {
    border-left: 4px solid #4e73df !important;
}
.border-left-success {
    border-left: 4px solid #1cc88a !important;
}
.border-left-info {
    border-left: 4px solid #36b9cc !important;
}
.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 13px;
    color: #6c757d;
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize charts
document.addEventListener('DOMContentLoaded', function() {
    initTournamentChart();
    initStatusChart();
});

function initTournamentChart() {
    const ctx = document.getElementById('tournamentChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Tournaments Created',
                data: [12, 19, 3, 5, 2, 3],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function initStatusChart() {
    const ctx = document.getElementById('statusChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Active', 'Completed', 'Upcoming', 'Cancelled'],
            datasets: [{
                data: [30, 45, 20, 5],
                backgroundColor: ['#1cc88a', '#4e73df', '#f6c23e', '#e74a3b']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function refreshDashboard() {
    location.reload();
}

function saveSystemSettings() {
    const form = document.getElementById('systemSettingsForm');
    const formData = new FormData(form);
    
    // Show loading state
    const saveBtn = event.target;
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<span class="loading-spinner"></span> Saving...';
    saveBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        EsportsApp.showToast('Settings saved successfully!', 'success');
        bootstrap.Modal.getInstance(document.getElementById('systemSettingsModal')).hide();
        
        // Restore button
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    }, 1000);
}
</script>
{% endblock %}
