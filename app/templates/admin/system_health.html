{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1><i class="fas fa-heartbeat"></i> System Health Monitor</h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <button type="button" class="btn btn-outline-primary" onclick="refreshMetrics()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <button type="button" class="btn btn-outline-success" onclick="downloadReport()">
                    <i class="fas fa-download"></i> Download Report
                </button>
            </div>
        </div>
    </div>
</div>

<!-- System Status Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">System Status</h5>
                <span class="badge bg-{% if health_data.status == 'healthy' %}success{% elif health_data.status == 'warning' %}warning{% else %}danger{% endif %} fs-6">
                    {% if health_data.status == 'healthy' %}
                    <i class="fas fa-check-circle"></i> Healthy
                    {% elif health_data.status == 'warning' %}
                    <i class="fas fa-exclamation-triangle"></i> Warning
                    {% else %}
                    <i class="fas fa-times-circle"></i> Critical
                    {% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- CPU Usage -->
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <div class="progress-circle mb-2" data-percentage="{{ health_data.cpu_usage }}">
                                <div class="progress-circle-inner">
                                    <span class="percentage">{{ health_data.cpu_usage }}%</span>
                                </div>
                            </div>
                            <h6>CPU Usage</h6>
                            <small class="text-muted">
                                {% if health_data.cpu_usage < 70 %}
                                <i class="fas fa-check text-success"></i> Normal
                                {% elif health_data.cpu_usage < 85 %}
                                <i class="fas fa-exclamation-triangle text-warning"></i> High
                                {% else %}
                                <i class="fas fa-times text-danger"></i> Critical
                                {% endif %}
                            </small>
                        </div>
                    </div>
                    
                    <!-- Memory Usage -->
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <div class="progress-circle mb-2" data-percentage="{{ health_data.memory_usage }}">
                                <div class="progress-circle-inner">
                                    <span class="percentage">{{ health_data.memory_usage }}%</span>
                                </div>
                            </div>
                            <h6>Memory Usage</h6>
                            <small class="text-muted">{{ health_data.memory_available }}GB available</small>
                        </div>
                    </div>
                    
                    <!-- Disk Usage -->
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <div class="progress-circle mb-2" data-percentage="{{ health_data.disk_usage }}">
                                <div class="progress-circle-inner">
                                    <span class="percentage">{{ health_data.disk_usage }}%</span>
                                </div>
                            </div>
                            <h6>Disk Usage</h6>
                            <small class="text-muted">{{ health_data.disk_free }}GB free</small>
                        </div>
                    </div>
                    
                    <!-- Database Connections -->
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <div class="display-4 text-primary mb-2">{{ health_data.db_connections }}</div>
                            <h6>DB Connections</h6>
                            <small class="text-muted">Active connections</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Database Statistics -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-database"></i> Database Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <h3 class="text-primary">{{ db_stats.total_tournaments }}</h3>
                        <p class="text-muted">Tournaments</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h3 class="text-success">{{ db_stats.total_users }}</h3>
                        <p class="text-muted">Users</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h3 class="text-info">{{ db_stats.total_matches }}</h3>
                        <p class="text-muted">Matches</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6 text-center">
                        <h4 class="text-warning">{{ db_stats.total_teams }}</h4>
                        <p class="text-muted">Teams</p>
                    </div>
                    <div class="col-md-6 text-center">
                        <h4 class="text-danger">{{ db_stats.total_streams }}</h4>
                        <p class="text-muted">Streams</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock"></i> Uptime</h5>
            </div>
            <div class="card-body text-center">
                <div class="display-6 text-success mb-2" id="uptime">
                    <i class="fas fa-server"></i>
                </div>
                <h4 id="uptimeDisplay">Calculating...</h4>
                <p class="text-muted">System uptime</p>
                
                <hr>
                
                <div class="text-center">
                    <h6>Last Restart</h6>
                    <small class="text-muted" id="lastRestart">Unknown</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent System Events -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> Recent System Events</h5>
            </div>
            <div class="card-body">
                {% if recent_errors %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Level</th>
                                <th>Source</th>
                                <th>Message</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for error in recent_errors %}
                            <tr>
                                <td>
                                    <small>{{ error.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-{% if error.level == 'ERROR' %}danger{% elif error.level == 'WARNING' %}warning{% else %}info{% endif %}">
                                        {{ error.level }}
                                    </span>
                                </td>
                                <td>{{ error.source }}</td>
                                <td>{{ error.message }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5>No Recent Issues</h5>
                    <p class="text-muted">System is running smoothly.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Real-time Metrics Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Real-time System Metrics</h5>
            </div>
            <div class="card-body">
                <canvas id="systemMetricsChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.progress-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(
        var(--bs-primary) calc(var(--percentage) * 1%),
        #e9ecef calc(var(--percentage) * 1%)
    );
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
}

.progress-circle-inner {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.percentage {
    font-weight: bold;
    font-size: 0.9rem;
}

/* Dynamic colors based on percentage */
.progress-circle[data-percentage] {
    --percentage: attr(data-percentage);
}

.progress-circle[data-percentage^="9"],
.progress-circle[data-percentage^="8"] {
    background: conic-gradient(
        #dc3545 calc(var(--percentage) * 1%),
        #e9ecef calc(var(--percentage) * 1%)
    );
}

.progress-circle[data-percentage^="7"] {
    background: conic-gradient(
        #ffc107 calc(var(--percentage) * 1%),
        #e9ecef calc(var(--percentage) * 1%)
    );
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let metricsChart;
let metricsData = {
    labels: [],
    cpu: [],
    memory: [],
    disk: []
};

// Initialize real-time metrics chart
function initMetricsChart() {
    const ctx = document.getElementById('systemMetricsChart').getContext('2d');
    metricsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: metricsData.labels,
            datasets: [
                {
                    label: 'CPU Usage (%)',
                    data: metricsData.cpu,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Memory Usage (%)',
                    data: metricsData.memory,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Disk Usage (%)',
                    data: metricsData.disk,
                    borderColor: 'rgb(255, 205, 86)',
                    backgroundColor: 'rgba(255, 205, 86, 0.1)',
                    tension: 0.1
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
}

// Refresh system metrics
function refreshMetrics() {
    fetch('/admin/api/system-metrics')
        .then(response => response.json())
        .then(data => {
            // Update progress circles
            updateProgressCircle('cpu', data.cpu_usage);
            updateProgressCircle('memory', data.memory_usage);
            updateProgressCircle('disk', data.disk_usage);
            
            // Update chart data
            const now = new Date().toLocaleTimeString();
            metricsData.labels.push(now);
            metricsData.cpu.push(data.cpu_usage);
            metricsData.memory.push(data.memory_usage);
            metricsData.disk.push(data.disk_usage);
            
            // Keep only last 20 data points
            if (metricsData.labels.length > 20) {
                metricsData.labels.shift();
                metricsData.cpu.shift();
                metricsData.memory.shift();
                metricsData.disk.shift();
            }
            
            metricsChart.update();
        })
        .catch(error => {
            console.error('Error fetching metrics:', error);
        });
}

function updateProgressCircle(type, percentage) {
    const circles = document.querySelectorAll('.progress-circle');
    // Implementation would update the specific circle
}

function downloadReport() {
    // Placeholder for report download
    alert('System health report download would be implemented here');
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initMetricsChart();
    
    // Refresh metrics every 30 seconds
    setInterval(refreshMetrics, 30000);
    
    // Initial metrics load
    refreshMetrics();
    
    // Update uptime display
    updateUptimeDisplay();
});

function updateUptimeDisplay() {
    // Placeholder uptime calculation
    const startTime = new Date(Date.now() - (Math.random() * 86400000 * 7)); // Random uptime up to 7 days
    const now = new Date();
    const uptime = now - startTime;
    
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    
    document.getElementById('uptimeDisplay').textContent = `${days}d ${hours}h ${minutes}m`;
    document.getElementById('lastRestart').textContent = startTime.toLocaleString();
}
</script>
{% endblock %}
