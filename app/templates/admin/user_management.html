{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1><i class="fas fa-users"></i> User Management</h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#bulkActionsModal">
                    <i class="fas fa-tasks"></i> Bulk Actions
                </button>
            </div>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card border-left-primary">
            <div class="card-body text-center">
                <h4 class="text-primary">{{ user_stats.total_users }}</h4>
                <p class="text-muted mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card border-left-success">
            <div class="card-body text-center">
                <h4 class="text-success">{{ user_stats.active_users }}</h4>
                <p class="text-muted mb-0">Active Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-left-warning">
            <div class="card-body text-center">
                <h4 class="text-warning">{{ user_stats.admin_users }}</h4>
                <p class="text-muted mb-0">Admins</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-left-info">
            <div class="card-body text-center">
                <h4 class="text-info">{{ user_stats.organizer_users }}</h4>
                <p class="text-muted mb-0">Organizers</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-left-secondary">
            <div class="card-body text-center">
                <h4 class="text-secondary">{{ user_stats.player_users }}</h4>
                <p class="text-muted mb-0">Players</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="Username or email...">
            </div>
            <div class="col-md-2">
                <label for="role" class="form-label">Role</label>
                <select class="form-select" id="role" name="role">
                    <option value="">All Roles</option>
                    <option value="admin" {% if role_filter == 'admin' %}selected{% endif %}>Admin</option>
                    <option value="organizer" {% if role_filter == 'organizer' %}selected{% endif %}>Organizer</option>
                    <option value="player" {% if role_filter == 'player' %}selected{% endif %}>Player</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactive</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{{ url_for('admin.user_management') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Users ({{ users.total }} total)</h5>
    </div>
    <div class="card-body">
        {% if users.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>User</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Joined</th>
                        <th>Last Activity</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr>
                        <td>
                            <input type="checkbox" class="user-checkbox" value="{{ user.id }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-primary rounded-circle">
                                        {{ user.username[0].upper() }}
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-bold">{{ user.username }}</div>
                                    <small class="text-muted">{{ user.email }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{% if user.role.name == 'ADMIN' %}danger{% elif user.role.name == 'ORGANIZER' %}warning{% else %}secondary{% endif %}">
                                {{ user.role.value.title() }}
                            </span>
                        </td>
                        <td>
                            {% if user.is_active %}
                            <span class="badge bg-success">Active</span>
                            {% else %}
                            <span class="badge bg-secondary">Inactive</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ user.created_at.strftime('%Y-%m-%d') }}</small>
                        </td>
                        <td>
                            <small class="text-muted">{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Never' }}</small>
                        </td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <form method="POST" action="{{ url_for('admin.toggle_user_status', user_id=user.id) }}" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-{% if user.is_active %}pause{% else %}play{% endif %}"></i>
                                                {% if user.is_active %}Deactivate{% else %}Activate{% endif %}
                                            </button>
                                        </form>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="#" data-bs-toggle="modal" 
                                           data-bs-target="#changeRoleModal" data-user-id="{{ user.id }}" 
                                           data-username="{{ user.username }}" data-current-role="{{ user.role.name }}">
                                            <i class="fas fa-user-tag"></i> Change Role
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item text-primary" href="{{ url_for('tournaments.user_tournaments', user_id=user.id) }}">
                                            <i class="fas fa-eye"></i> View Profile
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if users.pages > 1 %}
        <nav aria-label="User pagination">
            <ul class="pagination justify-content-center">
                {% if users.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.user_management', page=users.prev_num, search=search_query, role=role_filter, status=status_filter) }}">Previous</a>
                </li>
                {% endif %}
                
                {% for page_num in users.iter_pages() %}
                {% if page_num %}
                {% if page_num != users.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.user_management', page=page_num, search=search_query, role=role_filter, status=status_filter) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
                {% endif %}
                {% endfor %}
                
                {% if users.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.user_management', page=users.next_num, search=search_query, role=role_filter, status=status_filter) }}">Next</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5>No users found</h5>
            <p class="text-muted">Try adjusting your search criteria.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Change Role Modal -->
<div class="modal fade" id="changeRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change User Role</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="changeRoleForm">
                <div class="modal-body">
                    <p>Change role for user: <strong id="modalUsername"></strong></p>
                    <div class="mb-3">
                        <label for="newRole" class="form-label">New Role</label>
                        <select class="form-select" id="newRole" name="role" required>
                            <option value="admin">Admin</option>
                            <option value="organizer">Organizer</option>
                            <option value="player">Player</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Change Role</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Handle role change modal
document.getElementById('changeRoleModal').addEventListener('show.bs.modal', function (event) {
    const button = event.relatedTarget;
    const userId = button.getAttribute('data-user-id');
    const username = button.getAttribute('data-username');
    const currentRole = button.getAttribute('data-current-role');
    
    document.getElementById('modalUsername').textContent = username;
    document.getElementById('newRole').value = currentRole.toLowerCase();
    document.getElementById('changeRoleForm').action = `/admin/users/${userId}/change-role`;
});

// Select all functionality
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}
</script>
{% endblock %}
