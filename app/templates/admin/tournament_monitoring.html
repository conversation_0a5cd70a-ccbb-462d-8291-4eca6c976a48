{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1><i class="fas fa-monitor-waveform"></i> Tournament Monitoring</h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <button type="button" class="btn btn-outline-primary" onclick="refreshMonitoring()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Tournament Status Overview -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card stats-card border-left-secondary">
            <div class="card-body text-center">
                <h4 class="text-secondary">{{ tournaments_by_status.draft|length }}</h4>
                <p class="text-muted mb-0">Draft</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-left-primary">
            <div class="card-body text-center">
                <h4 class="text-primary">{{ tournaments_by_status.registration_open|length }}</h4>
                <p class="text-muted mb-0">Registration Open</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-left-warning">
            <div class="card-body text-center">
                <h4 class="text-warning">{{ tournaments_by_status.registration_closed|length }}</h4>
                <p class="text-muted mb-0">Registration Closed</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-left-info">
            <div class="card-body text-center">
                <h4 class="text-info">{{ tournaments_by_status.in_progress|length }}</h4>
                <p class="text-muted mb-0">In Progress</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-left-success">
            <div class="card-body text-center">
                <h4 class="text-success">{{ tournaments_by_status.completed|length }}</h4>
                <p class="text-muted mb-0">Completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-left-danger">
            <div class="card-body text-center">
                <h4 class="text-danger">{{ problematic_tournaments|length }}</h4>
                <p class="text-muted mb-0">Issues</p>
            </div>
        </div>
    </div>
</div>

<!-- Problematic Tournaments Alert -->
{% if problematic_tournaments %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Tournaments Requiring Attention</h5>
            </div>
            <div class="card-body">
                {% for item in problematic_tournaments %}
                <div class="alert alert-warning d-flex justify-content-between align-items-center">
                    <div>
                        <strong>{{ item.tournament.name }}</strong>
                        <br>
                        <small class="text-muted">
                            Issues: 
                            {% for issue in item.issues %}
                            <span class="badge bg-warning text-dark">{{ issue }}</span>
                            {% endfor %}
                        </small>
                    </div>
                    <div>
                        <a href="{{ url_for('tournaments.view', id=item.tournament.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ url_for('tournaments.edit', id=item.tournament.id) }}" class="btn btn-sm btn-outline-warning">
                            <i class="fas fa-edit"></i> Fix
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Active Tournaments -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-play text-info"></i> In Progress Tournaments</h5>
            </div>
            <div class="card-body">
                {% if tournaments_by_status.in_progress %}
                {% for tournament in tournaments_by_status.in_progress %}
                <div class="d-flex justify-content-between align-items-center mb-3 p-2 border rounded">
                    <div>
                        <h6 class="mb-1">{{ tournament.name }}</h6>
                        <small class="text-muted">
                            {{ tournament.teams|length }} teams • 
                            Started: {{ tournament.start_date.strftime('%Y-%m-%d') if tournament.start_date }}
                        </small>
                        <br>
                        <small class="text-info">
                            <i class="fas fa-gamepad"></i> {{ tournament.matches|length }} matches
                        </small>
                    </div>
                    <div>
                        <a href="{{ url_for('tournaments.view', id=tournament.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                    <h6>No tournaments in progress</h6>
                    <p class="text-muted">All tournaments are either completed or not yet started.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock text-warning"></i> Registration Closing Soon</h5>
            </div>
            <div class="card-body">
                {% if tournaments_by_status.registration_open %}
                {% for tournament in tournaments_by_status.registration_open %}
                <div class="d-flex justify-content-between align-items-center mb-3 p-2 border rounded">
                    <div>
                        <h6 class="mb-1">{{ tournament.name }}</h6>
                        <small class="text-muted">
                            {{ tournament.teams|length }}/{{ tournament.max_teams or '∞' }} teams
                        </small>
                        <br>
                        <small class="text-warning">
                            <i class="fas fa-calendar"></i> 
                            {% if tournament.registration_deadline %}
                            Deadline: {{ tournament.registration_deadline.strftime('%Y-%m-%d') }}
                            {% else %}
                            No deadline set
                            {% endif %}
                        </small>
                    </div>
                    <div>
                        <a href="{{ url_for('tournaments.view', id=tournament.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                    <h6>No open registrations</h6>
                    <p class="text-muted">No tournaments are currently accepting registrations.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history"></i> Recent Tournament Activity</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Tournament</th>
                                <th>Status</th>
                                <th>Teams</th>
                                <th>Matches</th>
                                <th>Created</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for status_key, tournaments in tournaments_by_status.items() %}
                            {% for tournament in tournaments[:5] %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ tournament.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ tournament.game }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{% if tournament.status.name == 'COMPLETED' %}success{% elif tournament.status.name == 'IN_PROGRESS' %}info{% elif tournament.status.name == 'REGISTRATION_OPEN' %}primary{% else %}secondary{% endif %}">
                                        {{ tournament.status.value.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>{{ tournament.teams|length }}</td>
                                <td>{{ tournament.matches|length }}</td>
                                <td>
                                    <small>{{ tournament.created_at.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>
                                    <small>{{ tournament.updated_at.strftime('%Y-%m-%d %H:%M') if tournament.updated_at else 'N/A' }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('tournaments.view', id=tournament.id) }}" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('tournaments.edit', id=tournament.id) }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tournament Performance Metrics -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Tournament Completion Rate</h6>
            </div>
            <div class="card-body text-center">
                <div class="display-4 text-success mb-2">
                    {% set total_tournaments = tournaments_by_status.completed|length + tournaments_by_status.in_progress|length + tournaments_by_status.registration_closed|length %}
                    {% if total_tournaments > 0 %}
                    {{ ((tournaments_by_status.completed|length / total_tournaments) * 100)|round(1) }}%
                    {% else %}
                    0%
                    {% endif %}
                </div>
                <p class="text-muted">Tournaments completed successfully</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Average Tournament Duration</h6>
            </div>
            <div class="card-body text-center">
                <div class="display-4 text-info mb-2">7.5</div>
                <p class="text-muted">Days (estimated)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Active Organizers</h6>
            </div>
            <div class="card-body text-center">
                <div class="display-4 text-warning mb-2">
                    {% set active_organizers = [] %}
                    {% for status_key, tournaments in tournaments_by_status.items() %}
                    {% for tournament in tournaments %}
                    {% if tournament.organizer not in active_organizers %}
                    {% set _ = active_organizers.append(tournament.organizer) %}
                    {% endif %}
                    {% endfor %}
                    {% endfor %}
                    {{ active_organizers|length }}
                </div>
                <p class="text-muted">Organizers with active tournaments</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshMonitoring() {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    button.disabled = true;
    
    // Simulate refresh
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Auto-refresh every 5 minutes
setInterval(() => {
    location.reload();
}, 300000);

// Add real-time status indicators
document.addEventListener('DOMContentLoaded', function() {
    // Add pulsing animation to in-progress tournaments
    const inProgressCards = document.querySelectorAll('.text-info');
    inProgressCards.forEach(card => {
        if (card.textContent.includes('In Progress')) {
            card.classList.add('pulse-animation');
        }
    });
});
</script>

<style>
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
