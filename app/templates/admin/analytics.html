{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1><i class="fas fa-chart-line"></i> Advanced Analytics</h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <button type="button" class="btn btn-outline-primary" onclick="exportAnalytics()">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Tournament Analytics -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-trophy"></i> Tournament Analytics</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-primary">{{ tournament_analytics.total_tournaments }}</h3>
                            <p class="text-muted">Total Tournaments</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-success">{{ tournament_analytics.completed_tournaments }}</h3>
                            <p class="text-muted">Completed</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-info">{{ tournament_analytics.completion_rate }}%</h3>
                            <p class="text-muted">Completion Rate</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-warning">{{ tournament_analytics.avg_duration_days }}</h3>
                            <p class="text-muted">Avg Duration (Days)</p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <h6>Tournament Creation Over Time (Last 30 Days)</h6>
                        <canvas id="tournamentCreationChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Analytics -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users"></i> User Analytics</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-primary">{{ user_analytics.total_users }}</h3>
                            <p class="text-muted">Total Users</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-success">{{ user_analytics.active_users }}</h3>
                            <p class="text-muted">Active Users</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-info">{{ user_analytics.activity_rate }}%</h3>
                            <p class="text-muted">Activity Rate</p>
                        </div>
                    </div>
                </div>
                
                <h6>User Registration Over Time (Last 30 Days)</h6>
                <canvas id="userRegistrationChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-video"></i> Stream Analytics</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h3 class="text-danger">{{ stream_analytics.active_streams }}</h3>
                    <p class="text-muted">Active Streams</p>
                </div>
                
                <div class="text-center mb-3">
                    <h4 class="text-info">{{ stream_analytics.activity_rate }}%</h4>
                    <p class="text-muted">Stream Activity Rate</p>
                </div>
                
                <h6>Platform Distribution</h6>
                <canvas id="streamPlatformChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Match Analytics -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-gamepad"></i> Match Analytics</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-primary">{{ match_analytics.completion_over_time.data|sum }}</h3>
                            <p class="text-muted">Matches Completed (30d)</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-warning">{{ match_analytics.avg_duration_minutes }}</h3>
                            <p class="text-muted">Avg Duration (Min)</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-success">{{ match_analytics.completion_rate }}%</h3>
                            <p class="text-muted">Completion Rate</p>
                        </div>
                    </div>
                </div>
                
                <h6>Match Completion Over Time (Last 30 Days)</h6>
                <canvas id="matchCompletionChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Match Status</h5>
            </div>
            <div class="card-body">
                <canvas id="matchStatusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Tournament Creation Chart
const tournamentCtx = document.getElementById('tournamentCreationChart').getContext('2d');
new Chart(tournamentCtx, {
    type: 'line',
    data: {
        labels: {{ tournament_analytics.creation_over_time.labels | tojson }},
        datasets: [{
            label: 'Tournaments Created',
            data: {{ tournament_analytics.creation_over_time.data | tojson }},
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// User Registration Chart
const userCtx = document.getElementById('userRegistrationChart').getContext('2d');
new Chart(userCtx, {
    type: 'bar',
    data: {
        labels: {{ user_analytics.registration_over_time.labels | tojson }},
        datasets: [{
            label: 'New Users',
            data: {{ user_analytics.registration_over_time.data | tojson }},
            backgroundColor: 'rgba(75, 192, 192, 0.6)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Stream Platform Chart
const streamCtx = document.getElementById('streamPlatformChart').getContext('2d');
new Chart(streamCtx, {
    type: 'doughnut',
    data: {
        labels: {{ stream_analytics.platform_distribution.labels | tojson }},
        datasets: [{
            data: {{ stream_analytics.platform_distribution.data | tojson }},
            backgroundColor: [
                '#9146ff',  // Twitch purple
                '#ff0000',  // YouTube red
                '#1da1f2'   // Other blue
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Match Completion Chart
const matchCtx = document.getElementById('matchCompletionChart').getContext('2d');
new Chart(matchCtx, {
    type: 'line',
    data: {
        labels: {{ match_analytics.completion_over_time.labels | tojson }},
        datasets: [{
            label: 'Matches Completed',
            data: {{ match_analytics.completion_over_time.data | tojson }},
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Match Status Chart
const matchStatusCtx = document.getElementById('matchStatusChart').getContext('2d');
new Chart(matchStatusCtx, {
    type: 'pie',
    data: {
        labels: {{ match_analytics.status_distribution.labels | tojson }},
        datasets: [{
            data: {{ match_analytics.status_distribution.data | tojson }},
            backgroundColor: [
                '#28a745',  // Completed - green
                '#ffc107',  // In Progress - yellow
                '#6c757d',  // Scheduled - gray
                '#dc3545'   // Cancelled - red
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

function exportAnalytics() {
    // Placeholder for export functionality
    alert('Export functionality would be implemented here');
}
</script>
{% endblock %}
