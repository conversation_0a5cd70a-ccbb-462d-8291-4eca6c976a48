{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1><i class="fas fa-file-alt"></i> Comprehensive Reports</h1>
            <div class="btn-group" role="group">
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <button type="button" class="btn btn-outline-primary" onclick="generateAllReports()">
                    <i class="fas fa-sync-alt"></i> Refresh All
                </button>
                <button type="button" class="btn btn-outline-success" onclick="exportAllReports()">
                    <i class="fas fa-download"></i> Export All
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Report Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Tournament Report
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                            {{ reports_data.tournament_report.completion_rate }}% Completion Rate
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-trophy fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            User Report
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                            {{ reports_data.user_report.activity_rate }}% Activity Rate
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Match Report
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                            {{ reports_data.match_report.completion_rate }}% Completion Rate
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-gamepad fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Financial Report
                        </div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                            ${{ reports_data.financial_report.total_revenue }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tournament Report -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-trophy"></i> Tournament Report</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="exportReport('tournament')">
                    <i class="fas fa-download"></i> Export
                </button>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-primary">{{ reports_data.tournament_report.total_tournaments }}</h4>
                            <small class="text-muted">Total Tournaments</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-success">{{ reports_data.tournament_report.completed_tournaments }}</h4>
                            <small class="text-muted">Completed</small>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-info">{{ reports_data.tournament_report.in_progress_tournaments }}</h4>
                            <small class="text-muted">In Progress</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-warning">{{ reports_data.tournament_report.avg_participants }}</h4>
                            <small class="text-muted">Avg Participants</small>
                        </div>
                    </div>
                </div>
                
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" style="width: {{ reports_data.tournament_report.completion_rate }}%"></div>
                </div>
                <small class="text-muted">{{ reports_data.tournament_report.completion_rate }}% completion rate</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users"></i> User Report</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="exportReport('user')">
                    <i class="fas fa-download"></i> Export
                </button>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-primary">{{ reports_data.user_report.total_users }}</h4>
                            <small class="text-muted">Total Users</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-success">{{ reports_data.user_report.active_users }}</h4>
                            <small class="text-muted">Active Users</small>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="text-center">
                            <h4 class="text-info">{{ reports_data.user_report.new_users_30d }}</h4>
                            <small class="text-muted">New Users (30 days)</small>
                        </div>
                    </div>
                </div>
                
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" style="width: {{ reports_data.user_report.activity_rate }}%"></div>
                </div>
                <small class="text-muted">{{ reports_data.user_report.activity_rate }}% activity rate</small>
            </div>
        </div>
    </div>
</div>

<!-- Match and Financial Reports -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-gamepad"></i> Match Report</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="exportReport('match')">
                    <i class="fas fa-download"></i> Export
                </button>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-primary">{{ reports_data.match_report.total_matches }}</h4>
                            <small class="text-muted">Total Matches</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-success">{{ reports_data.match_report.completed_matches }}</h4>
                            <small class="text-muted">Completed</small>
                        </div>
                    </div>
                </div>
                
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" style="width: {{ reports_data.match_report.completion_rate }}%"></div>
                </div>
                <small class="text-muted">{{ reports_data.match_report.completion_rate }}% completion rate</small>
                
                <hr>
                
                <div class="text-center">
                    <h6>Match Performance</h6>
                    <p class="text-muted">Detailed match analytics and performance metrics would be displayed here.</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-dollar-sign"></i> Financial Report</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="exportReport('financial')">
                    <i class="fas fa-download"></i> Export
                </button>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="text-center">
                            <h3 class="text-success">${{ reports_data.financial_report.total_revenue }}</h3>
                            <small class="text-muted">Total Revenue</small>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-primary">{{ reports_data.financial_report.tournaments_with_fees }}</h4>
                            <small class="text-muted">Paid Tournaments</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-info">${{ reports_data.financial_report.avg_entry_fee }}</h4>
                            <small class="text-muted">Avg Entry Fee</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <h6>Revenue Trends</h6>
                    <p class="text-muted">Financial analytics and revenue trends would be displayed here.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Reports Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table"></i> Detailed Report Data</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="reportsTable">
                        <thead>
                            <tr>
                                <th>Report Type</th>
                                <th>Generated</th>
                                <th>Status</th>
                                <th>Key Metrics</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><i class="fas fa-trophy text-primary"></i> Tournament Report</td>
                                <td>{{ moment().format('YYYY-MM-DD HH:mm') }}</td>
                                <td><span class="badge bg-success">Complete</span></td>
                                <td>{{ reports_data.tournament_report.total_tournaments }} tournaments, {{ reports_data.tournament_report.completion_rate }}% completion</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewReport('tournament')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="exportReport('tournament')">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-users text-success"></i> User Report</td>
                                <td>{{ moment().format('YYYY-MM-DD HH:mm') }}</td>
                                <td><span class="badge bg-success">Complete</span></td>
                                <td>{{ reports_data.user_report.total_users }} users, {{ reports_data.user_report.activity_rate }}% active</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewReport('user')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="exportReport('user')">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-gamepad text-info"></i> Match Report</td>
                                <td>{{ moment().format('YYYY-MM-DD HH:mm') }}</td>
                                <td><span class="badge bg-success">Complete</span></td>
                                <td>{{ reports_data.match_report.total_matches }} matches, {{ reports_data.match_report.completion_rate }}% completed</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewReport('match')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="exportReport('match')">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-dollar-sign text-warning"></i> Financial Report</td>
                                <td>{{ moment().format('YYYY-MM-DD HH:mm') }}</td>
                                <td><span class="badge bg-success">Complete</span></td>
                                <td>${{ reports_data.financial_report.total_revenue }} revenue, {{ reports_data.financial_report.tournaments_with_fees }} paid tournaments</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewReport('financial')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="exportReport('financial')">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function generateAllReports() {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    button.disabled = true;
    
    // Simulate report generation
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Show success message
        showAlert('All reports have been refreshed successfully!', 'success');
    }, 2000);
}

function exportAllReports() {
    // Placeholder for export all functionality
    showAlert('Exporting all reports... This feature would generate a comprehensive report package.', 'info');
}

function exportReport(reportType) {
    // Placeholder for individual report export
    showAlert(`Exporting ${reportType} report... This would generate a downloadable file.`, 'info');
}

function viewReport(reportType) {
    // Placeholder for detailed report view
    showAlert(`Opening detailed ${reportType} report... This would show an expanded view.`, 'info');
}

function showAlert(message, type) {
    // Create and show bootstrap alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at top of content
    const content = document.querySelector('.container-fluid');
    content.insertBefore(alertDiv, content.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Initialize DataTable for reports table
document.addEventListener('DOMContentLoaded', function() {
    // Would initialize DataTable here if using it
    console.log('Reports page loaded');
});
</script>
{% endblock %}
