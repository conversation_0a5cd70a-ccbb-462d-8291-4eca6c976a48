{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-trophy"></i> Tournament Leaderboards</h1>
    <a href="{{ url_for('leaderboards.global_leaderboard') }}" class="btn btn-primary">
        <i class="fas fa-globe"></i> Global Rankings
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="status" class="form-label">Tournament Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Tournaments</option>
                    <option value="in_progress" {% if current_status == 'in_progress' %}selected{% endif %}>In Progress</option>
                    <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>Completed</option>
                </select>
            </div>
            <div class="col-md-6 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter"></i> Filter
                </button>
                <a href="{{ url_for('leaderboards.index') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-times"></i> Clear
                </a>
                <a href="{{ url_for('leaderboards.global_leaderboard') }}" class="btn btn-outline-info">
                    <i class="fas fa-globe"></i> Global Rankings
                </a>
            </div>
        </form>
    </div>
</div>

{% if tournaments.items %}
<div class="row">
    {% for tournament in tournaments.items %}
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-trophy"></i> {{ tournament.name }}
                </h6>
                <span class="badge 
                    {% if tournament.status.value == 'in_progress' %}bg-primary
                    {% elif tournament.status.value == 'completed' %}bg-success
                    {% else %}bg-secondary{% endif %}">
                    {{ tournament.status.value.replace('_', ' ').title() }}
                </span>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Format</small><br>
                        <strong>{{ tournament.format.value.replace('_', ' ').title() }}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Teams</small><br>
                        <strong>{{ tournament.registered_teams_count }}</strong>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Game</small><br>
                        <strong>{{ tournament.game.title() }}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Prize Pool</small><br>
                        <strong>${{ tournament.prize_pool or 0 }}</strong>
                    </div>
                </div>
                
                {% if tournament.tournament_start %}
                <div class="mb-3">
                    <small class="text-muted">Started</small><br>
                    <strong>
                        <i class="fas fa-calendar"></i> 
                        {{ tournament.tournament_start.strftime('%B %d, %Y') }}
                    </strong>
                </div>
                {% endif %}
                
                {% if tournament.tournament_end and tournament.status.value == 'completed' %}
                <div class="mb-3">
                    <small class="text-muted">Completed</small><br>
                    <strong>
                        <i class="fas fa-flag-checkered"></i> 
                        {{ tournament.tournament_end.strftime('%B %d, %Y') }}
                    </strong>
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{{ url_for('leaderboards.tournament_leaderboard', tournament_id=tournament.id) }}" 
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-list-ol"></i> View Leaderboard
                    </a>
                    
                    {% if tournament.status.value == 'in_progress' %}
                    <span class="badge bg-warning">
                        <i class="fas fa-play"></i> Live
                    </span>
                    {% elif tournament.status.value == 'completed' %}
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i> Final
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if tournaments.pages > 1 %}
<nav aria-label="Tournaments pagination">
    <ul class="pagination justify-content-center">
        {% if tournaments.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('leaderboards.index', page=tournaments.prev_num, status=current_status) }}">
                <i class="fas fa-chevron-left"></i> Previous
            </a>
        </li>
        {% endif %}
        
        {% for page_num in tournaments.iter_pages() %}
            {% if page_num %}
                {% if page_num != tournaments.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('leaderboards.index', page=page_num, status=current_status) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if tournaments.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('leaderboards.index', page=tournaments.next_num, status=current_status) }}">
                Next <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No tournament leaderboards available</h3>
    <p class="text-muted">
        {% if current_status %}
            Try adjusting your filters to see more tournaments.
        {% else %}
            Leaderboards will appear here once tournaments are started.
        {% endif %}
    </p>
    {% if not current_status %}
    <a href="{{ url_for('tournaments.index') }}" class="btn btn-primary">
        <i class="fas fa-trophy"></i> View Tournaments
    </a>
    {% endif %}
</div>
{% endif %}

<!-- Quick Stats -->
<div class="row mt-5">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ tournaments.total }}</h4>
                        <small class="text-muted">Total Tournaments</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">
                            {% set live_count = tournaments.items | selectattr('status.value', 'equalto', 'in_progress') | list | length %}
                            {{ live_count }}
                        </h4>
                        <small class="text-muted">Live Tournaments</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">
                            {% set completed_count = tournaments.items | selectattr('status.value', 'equalto', 'completed') | list | length %}
                            {{ completed_count }}
                        </h4>
                        <small class="text-muted">Completed</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">
                            {% set total_teams = tournaments.items | sum(attribute='registered_teams_count') %}
                            {{ total_teams }}
                        </h4>
                        <small class="text-muted">Total Teams</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
