{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1><i class="fas fa-list-ol"></i> Tournament Leaderboard</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('leaderboards.index') }}">Leaderboards</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('tournaments.view', id=tournament.id) }}">{{ tournament.name }}</a></li>
                <li class="breadcrumb-item active">Leaderboard</li>
            </ol>
        </nav>
    </div>
    <div>
        <span class="badge badge-lg 
            {% if tournament.status.value == 'in_progress' %}bg-primary
            {% elif tournament.status.value == 'completed' %}bg-success
            {% else %}bg-secondary{% endif %}">
            {{ tournament.status.value.replace('_', ' ').title() }}
        </span>
    </div>
</div>

<!-- Tournament Info -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>Format</h6>
                        <p>{{ tournament.format.value.replace('_', ' ').title() }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Teams</h6>
                        <p>{{ tournament.registered_teams_count }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Game</h6>
                        <p>{{ tournament.game.title() }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Prize Pool</h6>
                        <p>${{ tournament.prize_pool or 0 }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('tournaments.view_bracket', id=tournament.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-sitemap"></i> View Bracket
                    </a>
                    <a href="{{ url_for('matches.index', tournament_id=tournament.id) }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-gamepad"></i> View Matches
                    </a>
                    {% if tournament.status.value == 'in_progress' %}
                    <button onclick="refreshLeaderboard()" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tournament Statistics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Tournament Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <h4 class="text-primary">{{ statistics.total_teams }}</h4>
                        <small class="text-muted">Teams</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-info">{{ statistics.total_matches }}</h4>
                        <small class="text-muted">Total Matches</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-success">{{ statistics.completed_matches }}</h4>
                        <small class="text-muted">Completed</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-warning">{{ statistics.completion_percentage }}%</h4>
                        <small class="text-muted">Progress</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-secondary">{{ statistics.total_games }}</h4>
                        <small class="text-muted">Games Played</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-dark">{{ statistics.average_match_duration }}m</h4>
                        <small class="text-muted">Avg Duration</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leaderboard -->
{% if leaderboard %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-trophy"></i> 
            {% if tournament.status.value == 'completed' %}Final {% endif %}Standings
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Rank</th>
                        <th>Team</th>
                        {% if tournament.format.value in ['round_robin', 'swiss'] %}
                        <th>Points</th>
                        {% endif %}
                        <th>Matches</th>
                        <th>W-L-D</th>
                        <th>Games</th>
                        <th>Diff</th>
                        <th>Win %</th>
                        {% if tournament.format.value == 'swiss' %}
                        <th>Buchholz</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for entry in leaderboard %}
                    <tr class="
                        {% if entry.rank == 1 %}table-warning
                        {% elif entry.rank <= 3 %}table-light
                        {% endif %}">
                        <td>
                            <strong class="
                                {% if entry.rank == 1 %}text-warning
                                {% elif entry.rank == 2 %}text-secondary
                                {% elif entry.rank == 3 %}text-warning
                                {% endif %}">
                                {% if entry.rank == 1 %}
                                    <i class="fas fa-trophy"></i>
                                {% elif entry.rank == 2 %}
                                    <i class="fas fa-medal"></i>
                                {% elif entry.rank == 3 %}
                                    <i class="fas fa-award"></i>
                                {% endif %}
                                {{ entry.rank }}
                            </strong>
                        </td>
                        <td>
                            <strong>{{ entry.team_name }}</strong>
                            <br><small class="text-muted">{{ entry.team_tag }}</small>
                        </td>
                        {% if tournament.format.value in ['round_robin', 'swiss'] %}
                        <td><strong>{{ entry.points }}</strong></td>
                        {% endif %}
                        <td>{{ entry.matches_played }}</td>
                        <td>
                            <span class="text-success">{{ entry.wins }}</span>-<span class="text-danger">{{ entry.losses }}</span>{% if entry.draws > 0 %}-<span class="text-warning">{{ entry.draws }}</span>{% endif %}
                        </td>
                        <td>{{ entry.games_won }}-{{ entry.games_lost }}</td>
                        <td class="{% if entry.game_differential > 0 %}text-success{% elif entry.game_differential < 0 %}text-danger{% endif %}">
                            {% if entry.game_differential > 0 %}+{% endif %}{{ entry.game_differential }}
                        </td>
                        <td>{{ entry.win_percentage }}%</td>
                        {% if tournament.format.value == 'swiss' %}
                        <td>{{ entry.buchholz_score }}</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Top Performers -->
{% if tournament.status.value == 'completed' and leaderboard|length >= 3 %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-podium"></i> Podium Finishers</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    {% for i in range(3) %}
                    {% if i < leaderboard|length %}
                    {% set entry = leaderboard[i] %}
                    <div class="col-md-4">
                        <div class="card border-{% if i == 0 %}warning{% elif i == 1 %}secondary{% else %}warning{% endif %}">
                            <div class="card-body">
                                <h1 class="text-{% if i == 0 %}warning{% elif i == 1 %}secondary{% else %}warning{% endif %}">
                                    {% if i == 0 %}
                                        <i class="fas fa-trophy"></i>
                                    {% elif i == 1 %}
                                        <i class="fas fa-medal"></i>
                                    {% else %}
                                        <i class="fas fa-award"></i>
                                    {% endif %}
                                </h1>
                                <h4>{{ entry.team_name }}</h4>
                                <p class="text-muted">{{ entry.team_tag }}</p>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>{{ entry.wins }}</strong><br>
                                        <small class="text-muted">Wins</small>
                                    </div>
                                    <div class="col-6">
                                        <strong>{{ entry.win_percentage }}%</strong><br>
                                        <small class="text-muted">Win Rate</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="fas fa-list-ol fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No leaderboard data available</h3>
    <p class="text-muted">
        {% if tournament.status.value == 'in_progress' %}
            Leaderboard will update as matches are completed.
        {% else %}
            No matches have been completed yet.
        {% endif %}
    </p>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function refreshLeaderboard() {
    // Show loading state
    const refreshBtn = document.querySelector('[onclick="refreshLeaderboard()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    // Reload the page after a short delay
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Auto-refresh for live tournaments every 60 seconds
{% if tournament.status.value == 'in_progress' %}
setInterval(() => {
    location.reload();
}, 60000);
{% endif %}
</script>
{% endblock %}
