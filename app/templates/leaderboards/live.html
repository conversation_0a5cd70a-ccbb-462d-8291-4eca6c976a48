{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-broadcast-tower"></i> Live Tournament Leaderboards</h1>
    <div>
        <button onclick="refreshAll()" class="btn btn-primary">
            <i class="fas fa-sync-alt"></i> Refresh All
        </button>
        <a href="{{ url_for('leaderboards.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-list"></i> All Leaderboards
        </a>
    </div>
</div>

{% if live_data %}
<div class="row">
    {% for data in live_data %}
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-trophy"></i> {{ data.tournament.name }}
                </h5>
                <div>
                    <span class="badge bg-primary">
                        <i class="fas fa-play"></i> Live
                    </span>
                    <span class="badge bg-info">
                        {{ data.statistics.completion_percentage }}% Complete
                    </span>
                </div>
            </div>
            <div class="card-body">
                <!-- Tournament Info -->
                <div class="row mb-3">
                    <div class="col-4">
                        <small class="text-muted">Format</small><br>
                        <strong>{{ data.tournament.format.value.replace('_', ' ').title() }}</strong>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Teams</small><br>
                        <strong>{{ data.tournament.registered_teams_count }}</strong>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Matches</small><br>
                        <strong>{{ data.statistics.completed_matches }}/{{ data.statistics.total_matches }}</strong>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="mb-3">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" 
                             style="width: {{ data.statistics.completion_percentage }}%"
                             aria-valuenow="{{ data.statistics.completion_percentage }}" 
                             aria-valuemin="0" aria-valuemax="100">
                            {{ data.statistics.completion_percentage }}%
                        </div>
                    </div>
                </div>

                <!-- Top 3 Teams -->
                {% if data.top_teams %}
                <h6 class="mb-2"><i class="fas fa-medal"></i> Current Top 3</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Team</th>
                                {% if data.tournament.format.value in ['round_robin', 'swiss'] %}
                                <th>Points</th>
                                {% endif %}
                                <th>W-L</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for team in data.top_teams %}
                            <tr class="{% if team.rank == 1 %}table-warning{% elif team.rank <= 3 %}table-light{% endif %}">
                                <td>
                                    <strong class="{% if team.rank == 1 %}text-warning{% elif team.rank == 2 %}text-secondary{% elif team.rank == 3 %}text-warning{% endif %}">
                                        {% if team.rank == 1 %}
                                            <i class="fas fa-trophy"></i>
                                        {% elif team.rank == 2 %}
                                            <i class="fas fa-medal"></i>
                                        {% elif team.rank == 3 %}
                                            <i class="fas fa-award"></i>
                                        {% endif %}
                                        {{ team.rank }}
                                    </strong>
                                </td>
                                <td>
                                    <strong>{{ team.team_name }}</strong><br>
                                    <small class="text-muted">{{ team.team_tag }}</small>
                                </td>
                                {% if data.tournament.format.value in ['round_robin', 'swiss'] %}
                                <td><strong>{{ team.points }}</strong></td>
                                {% endif %}
                                <td>
                                    <span class="text-success">{{ team.wins }}</span>-<span class="text-danger">{{ team.losses }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-hourglass-half text-muted"></i>
                    <p class="text-muted mb-0">Waiting for match results...</p>
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{{ url_for('leaderboards.tournament_leaderboard', tournament_id=data.tournament.id) }}" 
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-list-ol"></i> Full Leaderboard
                    </a>
                    <div>
                        <a href="{{ url_for('tournaments.view_bracket', id=data.tournament.id) }}" 
                           class="btn btn-outline-secondary btn-sm me-1">
                            <i class="fas fa-sitemap"></i> Bracket
                        </a>
                        <a href="{{ url_for('matches.index', tournament_id=data.tournament.id) }}" 
                           class="btn btn-outline-info btn-sm">
                            <i class="fas fa-gamepad"></i> Matches
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Live Statistics -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Live Tournament Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <h4 class="text-primary">{{ live_data|length }}</h4>
                        <small class="text-muted">Live Tournaments</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-info">
                            {% set total_teams = live_data | sum(attribute='tournament.registered_teams_count') %}
                            {{ total_teams }}
                        </h4>
                        <small class="text-muted">Active Teams</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-success">
                            {% set completed_matches = live_data | sum(attribute='statistics.completed_matches') %}
                            {{ completed_matches }}
                        </h4>
                        <small class="text-muted">Matches Completed</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-warning">
                            {% set total_matches = live_data | sum(attribute='statistics.total_matches') %}
                            {% set remaining_matches = total_matches - completed_matches %}
                            {{ remaining_matches }}
                        </h4>
                        <small class="text-muted">Matches Remaining</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-secondary">
                            {% set total_games = live_data | sum(attribute='statistics.total_games') %}
                            {{ total_games }}
                        </h4>
                        <small class="text-muted">Games Played</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-dark">
                            {% if live_data %}
                                {% set avg_completion = (live_data | sum(attribute='statistics.completion_percentage')) / live_data|length %}
                                {{ "%.1f"|format(avg_completion) }}%
                            {% else %}
                                0%
                            {% endif %}
                        </h4>
                        <small class="text-muted">Avg Progress</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="text-center py-5">
    <i class="fas fa-broadcast-tower fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No live tournaments</h3>
    <p class="text-muted">
        There are currently no tournaments in progress.<br>
        Check back later or view completed tournament leaderboards.
    </p>
    <div class="mt-4">
        <a href="{{ url_for('leaderboards.index') }}" class="btn btn-primary me-2">
            <i class="fas fa-list"></i> All Leaderboards
        </a>
        <a href="{{ url_for('tournaments.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-trophy"></i> View Tournaments
        </a>
    </div>
</div>
{% endif %}

<!-- Auto-refresh notification -->
<div class="alert alert-info mt-4" role="alert">
    <i class="fas fa-info-circle"></i>
    <strong>Live Updates:</strong> This page automatically refreshes every 30 seconds to show the latest results.
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshAll() {
    // Show loading state
    const refreshBtn = document.querySelector('[onclick="refreshAll()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    // Reload the page after a short delay
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Auto-refresh every 30 seconds for live tournaments
setInterval(() => {
    location.reload();
}, 30000);

// Add visual indicator for auto-refresh
let countdown = 30;
const updateCountdown = () => {
    countdown--;
    if (countdown <= 0) {
        countdown = 30;
    }
    
    // Update any countdown displays if they exist
    const countdownElements = document.querySelectorAll('.auto-refresh-countdown');
    countdownElements.forEach(el => {
        el.textContent = countdown;
    });
};

setInterval(updateCountdown, 1000);
</script>
{% endblock %}
