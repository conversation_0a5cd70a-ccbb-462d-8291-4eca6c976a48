{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1><i class="fas fa-globe"></i> Global Team Rankings</h1>
        <p class="text-muted">Top performing teams across all tournaments</p>
    </div>
    <div>
        <a href="{{ url_for('leaderboards.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-list"></i> Tournament Leaderboards
        </a>
        <a href="{{ url_for('leaderboards.index') }}" class="btn btn-primary">
            <i class="fas fa-trophy"></i> Tournament Leaderboards
        </a>
    </div>
</div>

{% if global_leaderboard %}
<!-- Top 3 Global Teams -->
<div class="row mb-4">
    {% for i in range(3) %}
    {% if i < global_leaderboard|length %}
    {% set team = global_leaderboard[i] %}
    <div class="col-md-4">
        <div class="card border-{% if i == 0 %}warning{% elif i == 1 %}secondary{% else %}warning{% endif %} h-100">
            <div class="card-body text-center">
                <h1 class="text-{% if i == 0 %}warning{% elif i == 1 %}secondary{% else %}warning{% endif %}">
                    {% if i == 0 %}
                        <i class="fas fa-crown"></i>
                    {% elif i == 1 %}
                        <i class="fas fa-medal"></i>
                    {% else %}
                        <i class="fas fa-award"></i>
                    {% endif %}
                </h1>
                <h3>{{ team.team_name }}</h3>
                <p class="text-muted">{{ team.team_tag }}</p>
                
                <div class="row">
                    <div class="col-6">
                        <h4 class="text-success">{{ team.first_places }}</h4>
                        <small class="text-muted">Championships</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ team.podium_finishes }}</h4>
                        <small class="text-muted">Podium Finishes</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-4">
                        <strong>{{ team.tournaments_played }}</strong><br>
                        <small class="text-muted">Tournaments</small>
                    </div>
                    <div class="col-4">
                        <strong>{{ team.total_wins }}</strong><br>
                        <small class="text-muted">Wins</small>
                    </div>
                    <div class="col-4">
                        <strong>{{ team.win_percentage }}%</strong><br>
                        <small class="text-muted">Win Rate</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% endfor %}
</div>

<!-- Full Global Rankings -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list-ol"></i> Complete Global Rankings</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Rank</th>
                        <th>Team</th>
                        <th>Championships</th>
                        <th>Podium Finishes</th>
                        <th>Tournaments</th>
                        <th>Total Matches</th>
                        <th>Wins</th>
                        <th>Win Rate</th>
                        <th>Performance Score</th>
                    </tr>
                </thead>
                <tbody>
                    {% for team in global_leaderboard %}
                    <tr class="
                        {% if team.rank == 1 %}table-warning
                        {% elif team.rank <= 3 %}table-light
                        {% elif team.rank <= 10 %}table-success
                        {% endif %}">
                        <td>
                            <strong class="
                                {% if team.rank == 1 %}text-warning
                                {% elif team.rank == 2 %}text-secondary
                                {% elif team.rank == 3 %}text-warning
                                {% elif team.rank <= 10 %}text-success
                                {% endif %}">
                                {% if team.rank == 1 %}
                                    <i class="fas fa-crown"></i>
                                {% elif team.rank == 2 %}
                                    <i class="fas fa-medal"></i>
                                {% elif team.rank == 3 %}
                                    <i class="fas fa-award"></i>
                                {% elif team.rank <= 10 %}
                                    <i class="fas fa-star"></i>
                                {% endif %}
                                {{ team.rank }}
                            </strong>
                        </td>
                        <td>
                            <strong>{{ team.team_name }}</strong>
                            <br><small class="text-muted">{{ team.team_tag }}</small>
                        </td>
                        <td>
                            <span class="badge bg-warning">{{ team.first_places }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ team.podium_finishes }}</span>
                        </td>
                        <td>{{ team.tournaments_played }}</td>
                        <td>{{ team.total_matches }}</td>
                        <td class="text-success">{{ team.total_wins }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="me-2">{{ team.win_percentage }}%</span>
                                <div class="progress flex-grow-1" style="height: 8px;">
                                    <div class="progress-bar 
                                        {% if team.win_percentage >= 80 %}bg-success
                                        {% elif team.win_percentage >= 60 %}bg-warning
                                        {% else %}bg-danger{% endif %}" 
                                         style="width: {{ team.win_percentage }}%"></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            {% set performance_score = (team.first_places * 10) + (team.podium_finishes * 5) + (team.win_percentage * 0.5) %}
                            <strong>{{ "%.1f"|format(performance_score) }}</strong>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Global Statistics -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Global Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <h4 class="text-primary">{{ global_leaderboard|length }}</h4>
                        <small class="text-muted">Active Teams</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-info">
                            {% set total_tournaments = global_leaderboard | sum(attribute='tournaments_played') %}
                            {{ total_tournaments }}
                        </h4>
                        <small class="text-muted">Total Tournaments</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-success">
                            {% set total_matches = global_leaderboard | sum(attribute='total_matches') %}
                            {{ total_matches }}
                        </h4>
                        <small class="text-muted">Matches Played</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-warning">
                            {% set total_championships = global_leaderboard | sum(attribute='first_places') %}
                            {{ total_championships }}
                        </h4>
                        <small class="text-muted">Championships</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-secondary">
                            {% if global_leaderboard %}
                                {% set avg_win_rate = (global_leaderboard | sum(attribute='win_percentage')) / global_leaderboard|length %}
                                {{ "%.1f"|format(avg_win_rate) }}%
                            {% else %}
                                0%
                            {% endif %}
                        </h4>
                        <small class="text-muted">Avg Win Rate</small>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-dark">
                            {% set most_active = global_leaderboard | max(attribute='tournaments_played') if global_leaderboard else 0 %}
                            {{ most_active.tournaments_played if most_active else 0 }}
                        </h4>
                        <small class="text-muted">Most Tournaments</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Categories -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-fire"></i> Most Dominant</h6>
            </div>
            <div class="card-body">
                {% set most_championships = global_leaderboard | selectattr('first_places', 'gt', 0) | sort(attribute='first_places', reverse=true) | list %}
                {% for team in most_championships[:5] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong>{{ team.team_name }}</strong><br>
                        <small class="text-muted">{{ team.team_tag }}</small>
                    </div>
                    <span class="badge bg-warning">{{ team.first_places }} 🏆</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-line"></i> Most Consistent</h6>
            </div>
            <div class="card-body">
                {% set most_consistent = global_leaderboard | selectattr('tournaments_played', 'gt', 2) | sort(attribute='win_percentage', reverse=true) | list %}
                {% for team in most_consistent[:5] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong>{{ team.team_name }}</strong><br>
                        <small class="text-muted">{{ team.team_tag }}</small>
                    </div>
                    <span class="badge bg-success">{{ team.win_percentage }}%</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-users"></i> Most Active</h6>
            </div>
            <div class="card-body">
                {% set most_active_teams = global_leaderboard | sort(attribute='tournaments_played', reverse=true) | list %}
                {% for team in most_active_teams[:5] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong>{{ team.team_name }}</strong><br>
                        <small class="text-muted">{{ team.team_tag }}</small>
                    </div>
                    <span class="badge bg-info">{{ team.tournaments_played }} tournaments</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="text-center py-5">
    <i class="fas fa-globe fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No global rankings available</h3>
    <p class="text-muted">
        Global rankings will appear here once tournaments are completed<br>
        and teams have match results to analyze.
    </p>
    <div class="mt-4">
        <a href="{{ url_for('tournaments.index') }}" class="btn btn-primary me-2">
            <i class="fas fa-trophy"></i> View Tournaments
        </a>
        <a href="{{ url_for('leaderboards.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-list"></i> Tournament Leaderboards
        </a>
    </div>
</div>
{% endif %}
{% endblock %}
