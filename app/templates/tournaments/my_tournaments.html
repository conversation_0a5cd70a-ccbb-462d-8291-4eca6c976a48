{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-user-cog"></i> My Tournaments</h1>
    <div class="btn-group">
        <a href="{{ url_for('tournaments.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Tournament
        </a>
        <a href="{{ url_for('tournaments.quick_create') }}" class="btn btn-outline-primary">
            <i class="fas fa-bolt"></i> Quick Create
        </a>
    </div>
</div>

{% if tournaments.items %}
<div class="row">
    {% for tournament in tournaments.items %}
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ tournament.name }}</h5>
                <span class="badge 
                    {% if tournament.status.value == 'draft' %}bg-secondary
                    {% elif tournament.status.value == 'registration_open' %}bg-success
                    {% elif tournament.status.value == 'registration_closed' %}bg-warning
                    {% elif tournament.status.value == 'in_progress' %}bg-primary
                    {% elif tournament.status.value == 'completed' %}bg-info
                    {% else %}bg-danger{% endif %}">
                    {{ tournament.status.value.replace('_', ' ').title() }}
                </span>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Game</small><br>
                        <strong>{{ tournament.game.title() }}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Teams Registered</small><br>
                        <strong>{{ tournament.registered_teams_count }}/{{ tournament.max_teams }}</strong>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Format</small><br>
                        <strong>{{ tournament.format.value.replace('_', ' ').title() }}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Prize Pool</small><br>
                        <strong>${{ tournament.prize_pool or 0 }}</strong>
                    </div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Created</small><br>
                    <strong>{{ tournament.created_at.strftime('%B %d, %Y') }}</strong>
                </div>
                
                <!-- Progress Bar -->
                {% set progress_percentage = (tournament.registered_teams_count / tournament.max_teams * 100) %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">Registration Progress</small>
                        <small class="text-muted">{{ progress_percentage|round|int }}%</small>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar 
                            {% if progress_percentage < 25 %}bg-danger
                            {% elif progress_percentage < 50 %}bg-warning
                            {% elif progress_percentage < 75 %}bg-info
                            {% else %}bg-success{% endif %}" 
                             style="width: {{ progress_percentage }}%"></div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row g-2">
                    <div class="col-6">
                        <a href="{{ url_for('tournaments.view', id=tournament.id) }}" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-eye"></i> View
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('tournaments.edit', id=tournament.id) }}" class="btn btn-outline-secondary btn-sm w-100">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                    </div>
                </div>
                
                <!-- Status-specific actions -->
                {% if tournament.status.value == 'draft' %}
                <div class="mt-2">
                    <form method="POST" action="{{ url_for('tournaments.open_registration', id=tournament.id) }}" class="d-inline">
                        <button type="submit" class="btn btn-success btn-sm w-100">
                            <i class="fas fa-door-open"></i> Open Registration
                        </button>
                    </form>
                </div>
                {% elif tournament.status.value == 'registration_open' %}
                <div class="mt-2">
                    <form method="POST" action="{{ url_for('tournaments.close_registration', id=tournament.id) }}" class="d-inline">
                        <button type="submit" class="btn btn-warning btn-sm w-100">
                            <i class="fas fa-door-closed"></i> Close Registration
                        </button>
                    </form>
                </div>
                {% elif tournament.status.value == 'registration_closed' %}
                <div class="mt-2">
                    <form method="POST" action="{{ url_for('tournaments.start_tournament', id=tournament.id) }}" class="d-inline">
                        <button type="submit" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-play"></i> Start Tournament
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        Starts {{ tournament.tournament_start.strftime('%m/%d/%Y') }}
                    </small>
                    {% if tournament.status.value == 'registration_open' %}
                    <span class="badge bg-success">
                        <i class="fas fa-door-open"></i> Open
                    </span>
                    {% elif tournament.status.value == 'in_progress' %}
                    <span class="badge bg-primary">
                        <i class="fas fa-play"></i> Live
                    </span>
                    {% elif tournament.status.value == 'completed' %}
                    <span class="badge bg-info">
                        <i class="fas fa-flag-checkered"></i> Finished
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if tournaments.pages > 1 %}
<nav aria-label="My tournaments pagination">
    <ul class="pagination justify-content-center">
        {% if tournaments.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('tournaments.my_tournaments', page=tournaments.prev_num) }}">
                <i class="fas fa-chevron-left"></i> Previous
            </a>
        </li>
        {% endif %}
        
        {% for page_num in tournaments.iter_pages() %}
            {% if page_num %}
                {% if page_num != tournaments.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('tournaments.my_tournaments', page=page_num) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if tournaments.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('tournaments.my_tournaments', page=tournaments.next_num) }}">
                Next <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No tournaments created yet</h3>
    <p class="text-muted">Create your first tournament to get started!</p>
    <div class="btn-group">
        <a href="{{ url_for('tournaments.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Tournament
        </a>
        <a href="{{ url_for('tournaments.quick_create') }}" class="btn btn-outline-primary">
            <i class="fas fa-bolt"></i> Quick Create
        </a>
    </div>
</div>
{% endif %}
{% endblock %}
