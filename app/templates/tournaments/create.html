{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-plus"></i> Create Tournament
                </h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                {{ form.game.label(class="form-label") }}
                                {{ form.game(class="form-select" + (" is-invalid" if form.game.errors else "")) }}
                                {% if form.game.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.game.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="4") }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Describe your tournament, rules, and any special information</div>
                    </div>
                    
                    <!-- Tournament Settings -->
                    <h5 class="mt-4 mb-3"><i class="fas fa-cog"></i> Tournament Settings</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                {{ form.format.label(class="form-label") }}
                                {{ form.format(class="form-select" + (" is-invalid" if form.format.errors else "")) }}
                                {% if form.format.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.format.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                {{ form.max_teams.label(class="form-label") }}
                                {{ form.max_teams(class="form-control" + (" is-invalid" if form.max_teams.errors else "")) }}
                                {% if form.max_teams.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.max_teams.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                {{ form.team_size.label(class="form-label") }}
                                {{ form.team_size(class="form-control" + (" is-invalid" if form.team_size.errors else "")) }}
                                {% if form.team_size.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.team_size.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Financial Settings -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.entry_fee.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    {{ form.entry_fee(class="form-control" + (" is-invalid" if form.entry_fee.errors else "")) }}
                                </div>
                                {% if form.entry_fee.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.entry_fee.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.prize_pool.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    {{ form.prize_pool(class="form-control" + (" is-invalid" if form.prize_pool.errors else "")) }}
                                </div>
                                {% if form.prize_pool.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.prize_pool.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Schedule -->
                    <h5 class="mt-4 mb-3"><i class="fas fa-calendar"></i> Schedule</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.registration_start.label(class="form-label") }}
                                {{ form.registration_start(class="form-control" + (" is-invalid" if form.registration_start.errors else "")) }}
                                {% if form.registration_start.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.registration_start.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.registration_end.label(class="form-label") }}
                                {{ form.registration_end(class="form-control" + (" is-invalid" if form.registration_end.errors else "")) }}
                                {% if form.registration_end.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.registration_end.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.tournament_start.label(class="form-label") }}
                                {{ form.tournament_start(class="form-control" + (" is-invalid" if form.tournament_start.errors else "")) }}
                                {% if form.tournament_start.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.tournament_start.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.tournament_end.label(class="form-label") }}
                                {{ form.tournament_end(class="form-control" + (" is-invalid" if form.tournament_end.errors else "")) }}
                                {% if form.tournament_end.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.tournament_end.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Settings -->
                    <h5 class="mt-4 mb-3"><i class="fas fa-info-circle"></i> Additional Information</h5>
                    <div class="mb-3">
                        {{ form.rules.label(class="form-label") }}
                        {{ form.rules(class="form-control" + (" is-invalid" if form.rules.errors else ""), rows="6") }}
                        {% if form.rules.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.rules.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Tournament rules, regulations, and guidelines</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.stream_url.label(class="form-label") }}
                                {{ form.stream_url(class="form-control" + (" is-invalid" if form.stream_url.errors else "")) }}
                                {% if form.stream_url.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.stream_url.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.discord_server.label(class="form-label") }}
                                {{ form.discord_server(class="form-control" + (" is-invalid" if form.discord_server.errors else "")) }}
                                {% if form.discord_server.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.discord_server.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('tournaments.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Tournaments
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
