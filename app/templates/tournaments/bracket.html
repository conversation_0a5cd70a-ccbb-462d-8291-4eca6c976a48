{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1><i class="fas fa-sitemap"></i> Tournament Bracket</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('tournaments.index') }}">Tournaments</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('tournaments.view', id=tournament.id) }}">{{ tournament.name }}</a></li>
                <li class="breadcrumb-item active">Bracket</li>
            </ol>
        </nav>
    </div>
    <div>
        <span class="badge badge-lg 
            {% if tournament.status.value == 'draft' %}bg-secondary
            {% elif tournament.status.value == 'registration_open' %}bg-success
            {% elif tournament.status.value == 'registration_closed' %}bg-warning
            {% elif tournament.status.value == 'in_progress' %}bg-primary
            {% elif tournament.status.value == 'completed' %}bg-info
            {% else %}bg-danger{% endif %}">
            {{ tournament.status.value.replace('_', ' ').title() }}
        </span>
    </div>
</div>

<!-- Tournament Info -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>Format</h6>
                        <p>{{ tournament.format.value.replace('_', ' ').title() }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Teams</h6>
                        <p>{{ tournament.registered_teams_count }}/{{ tournament.max_teams }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Game</h6>
                        <p>{{ tournament.game.title() }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Prize Pool</h6>
                        <p>${{ tournament.prize_pool or 0 }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        {% if current_user.is_authenticated and (current_user.id == tournament.organizer_id or current_user.role.value == 'admin') %}
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Organizer Actions</h6>
            </div>
            <div class="card-body">
                {% if tournament.format.value == 'swiss' and tournament.status.value == 'in_progress' %}
                <form method="POST" action="{{ url_for('tournaments.generate_next_round', id=tournament.id) }}">
                    <button type="submit" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-plus"></i> Generate Next Round
                    </button>
                </form>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Live Matches -->
{% if live_matches %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-play text-warning"></i> Live Matches</h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for match in live_matches %}
            <div class="col-md-6 mb-3">
                <div class="card border-warning">
                    <div class="card-body">
                        <h6 class="card-title">{{ match.teams_display }}</h6>
                        <p class="card-text">
                            <small class="text-muted">{{ match.bracket_position or 'Round ' + match.round_number|string }}</small>
                        </p>
                        <a href="{{ url_for('matches.view', id=match.id) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-eye"></i> Watch
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Next Matches -->
{% if next_matches %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-clock text-primary"></i> Upcoming Matches</h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for match in next_matches %}
            <div class="col-md-6 mb-3">
                <div class="card border-primary">
                    <div class="card-body">
                        <h6 class="card-title">{{ match.teams_display }}</h6>
                        <p class="card-text">
                            <small class="text-muted">
                                {{ match.bracket_position or 'Round ' + match.round_number|string }}
                                {% if match.scheduled_time %}
                                    <br><i class="fas fa-calendar"></i> {{ match.scheduled_time.strftime('%m/%d %I:%M %p') }}
                                {% endif %}
                            </small>
                        </p>
                        <a href="{{ url_for('matches.view', id=match.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Tournament Schedule -->
{% if schedule %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list"></i> Tournament Schedule</h5>
    </div>
    <div class="card-body">
        {% for round_name, matches in schedule.items() %}
        <div class="mb-4">
            <h6 class="border-bottom pb-2">{{ round_name }}</h6>
            <div class="row">
                {% for match in matches %}
                <div class="col-lg-6 mb-3">
                    <div class="card 
                        {% if match.status.value == 'completed' %}border-success
                        {% elif match.status.value == 'in_progress' %}border-warning
                        {% elif match.status.value == 'cancelled' %}border-danger
                        {% else %}border-light{% endif %}">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0">{{ match.teams_display }}</h6>
                                <span class="badge 
                                    {% if match.status.value == 'scheduled' %}bg-primary
                                    {% elif match.status.value == 'in_progress' %}bg-warning
                                    {% elif match.status.value == 'completed' %}bg-success
                                    {% elif match.status.value == 'cancelled' %}bg-secondary
                                    {% else %}bg-danger{% endif %}">
                                    {{ match.status.value.replace('_', ' ').title() }}
                                </span>
                            </div>
                            
                            {% if match.status.value == 'completed' %}
                            <div class="text-center mb-2">
                                <strong class="{% if match.winner_id == match.team1_id %}text-success{% endif %}">
                                    {{ match.team1_score }}
                                </strong>
                                <span class="mx-2">-</span>
                                <strong class="{% if match.winner_id == match.team2_id %}text-success{% endif %}">
                                    {{ match.team2_score }}
                                </strong>
                            </div>
                            {% endif %}
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    {% if match.scheduled_time %}
                                        <i class="fas fa-calendar"></i> {{ match.scheduled_time.strftime('%m/%d %I:%M %p') }}
                                    {% else %}
                                        TBD
                                    {% endif %}
                                </small>
                                <a href="{{ url_for('matches.view', id=match.id) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% else %}
<div class="text-center py-5">
    <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No bracket generated yet</h3>
    <p class="text-muted">
        {% if tournament.status.value == 'draft' %}
            Tournament is still in draft mode.
        {% elif tournament.status.value == 'registration_open' %}
            Bracket will be generated when the tournament starts.
        {% elif tournament.status.value == 'registration_closed' %}
            Start the tournament to generate the bracket.
        {% else %}
            No matches have been scheduled for this tournament.
        {% endif %}
    </p>
    {% if tournament.status.value == 'registration_closed' and current_user.is_authenticated and (current_user.id == tournament.organizer_id or current_user.role.value == 'admin') %}
    <a href="{{ url_for('tournaments.view', id=tournament.id) }}" class="btn btn-primary">
        <i class="fas fa-play"></i> Start Tournament
    </a>
    {% endif %}
</div>
{% endif %}

<!-- Enhanced Bracket Visualization -->
{% if tournament.format.value in ['single_elimination', 'double_elimination'] and schedule %}
<div class="card mt-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-project-diagram"></i> Interactive Bracket</h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary" onclick="zoomBracket('in')">
                <i class="fas fa-search-plus"></i>
            </button>
            <button class="btn btn-outline-primary" onclick="zoomBracket('out')">
                <i class="fas fa-search-minus"></i>
            </button>
            <button class="btn btn-outline-primary" onclick="resetBracketView()">
                <i class="fas fa-expand-arrows-alt"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- Enhanced bracket container with controls -->
        <div class="bracket-controls mb-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="showTeamLogos" checked>
                        <label class="form-check-label" for="showTeamLogos">Show Team Logos</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="animateBracket" checked>
                        <label class="form-check-label" for="animateBracket">Animate Updates</label>
                    </div>
                </div>
            </div>
        </div>

        <div id="bracket-container" class="bracket-visualization">
            <!-- Dynamic bracket will be rendered here -->
        </div>

        <!-- Bracket Legend -->
        <div class="bracket-legend mt-3">
            <div class="row">
                <div class="col-md-3">
                    <div class="legend-item">
                        <span class="legend-color bg-success"></span>
                        <span class="legend-text">Completed</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="legend-item">
                        <span class="legend-color bg-warning"></span>
                        <span class="legend-text">In Progress</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="legend-item">
                        <span class="legend-color bg-info"></span>
                        <span class="legend-text">Upcoming</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="legend-item">
                        <span class="legend-color bg-secondary"></span>
                        <span class="legend-text">TBD</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
// Enhanced bracket visualization
document.addEventListener('DOMContentLoaded', function() {
    {% if tournament.format.value in ['single_elimination', 'double_elimination'] and schedule %}
    initBracketVisualization();
    {% endif %}
});

function initBracketVisualization() {
    const tournamentData = {
        id: {{ tournament.id }},
        name: "{{ tournament.name }}",
        format: "{{ tournament.format.value }}",
        rounds: {{ schedule | tojson if schedule else '[]' }}
    };

    BracketVisualizer.init('bracket-container', tournamentData);
}

function zoomBracket(direction) {
    const container = document.getElementById('bracket-container');
    const currentScale = container.style.transform.match(/scale\(([^)]+)\)/);
    let scale = currentScale ? parseFloat(currentScale[1]) : 1;

    if (direction === 'in') {
        scale = Math.min(scale * 1.2, 3);
    } else {
        scale = Math.max(scale / 1.2, 0.5);
    }

    container.style.transform = `scale(${scale})`;
    container.style.transformOrigin = 'center center';
}

function resetBracketView() {
    const container = document.getElementById('bracket-container');
    container.style.transform = 'scale(1)';
}

// Auto-refresh live matches every 30 seconds
{% if live_matches %}
setInterval(function() {
    // Only refresh if there are live matches
    if (document.querySelectorAll('.border-warning').length > 0) {
        // Refresh bracket data without full page reload
        fetch(window.location.href)
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newSchedule = doc.querySelector('#tournament-schedule');
                const currentSchedule = document.querySelector('#tournament-schedule');

                if (newSchedule && currentSchedule) {
                    currentSchedule.innerHTML = newSchedule.innerHTML;
                    // Re-initialize bracket if needed
                    initBracketVisualization();
                }
            })
            .catch(error => console.error('Error refreshing bracket:', error));
    }
}, 30000);
{% endif %}

// Toggle bracket features
document.addEventListener('change', function(e) {
    if (e.target.id === 'showTeamLogos') {
        const logos = document.querySelectorAll('.team-logo');
        logos.forEach(logo => {
            logo.style.display = e.target.checked ? 'block' : 'none';
        });
    }

    if (e.target.id === 'animateBracket') {
        const container = document.getElementById('bracket-container');
        container.classList.toggle('animate-updates', e.target.checked);
    }
});
</script>

<style>
.bracket-visualization {
    min-height: 400px;
    overflow: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    position: relative;
    transition: transform 0.3s ease;
}

.bracket-controls {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
}

.bracket-legend {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 0.5rem;
    display: inline-block;
}

.legend-text {
    font-size: 0.875rem;
    font-weight: 500;
}

.bracket-container .bracket-round {
    min-width: 200px;
    padding: 1rem;
}

.bracket-match {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.bracket-match:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.bracket-match.completed {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.bracket-match.in-progress {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    animation: pulse 2s infinite;
}

.bracket-match.upcoming {
    border-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.bracket-match .team {
    padding: 0.5rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: between;
    align-items: center;
}

.bracket-match .team:last-child {
    border-bottom: none;
}

.bracket-match .team.winner {
    background: rgba(40, 167, 69, 0.1);
    font-weight: bold;
}

.animate-updates .bracket-match {
    transition: all 0.5s ease;
}

@media (max-width: 768px) {
    .bracket-container {
        overflow-x: auto;
    }

    .bracket-round {
        min-width: 150px;
    }
}
</style>
{% endblock %}
