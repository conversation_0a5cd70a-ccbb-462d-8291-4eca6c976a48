{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Create Tournament
                </h3>
                <small class="text-muted">Create a tournament with default settings in seconds</small>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.game.label(class="form-label") }}
                        {{ form.game(class="form-select" + (" is-invalid" if form.game.errors else "")) }}
                        {% if form.game.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.game.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.max_teams.label(class="form-label") }}
                                {{ form.max_teams(class="form-select" + (" is-invalid" if form.max_teams.errors else "")) }}
                                {% if form.max_teams.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.max_teams.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.team_size.label(class="form-label") }}
                                {{ form.team_size(class="form-select" + (" is-invalid" if form.team_size.errors else "")) }}
                                {% if form.team_size.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.team_size.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.registration_days.label(class="form-label") }}
                        {{ form.registration_days(class="form-select" + (" is-invalid" if form.registration_days.errors else "")) }}
                        {% if form.registration_days.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.registration_days.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">How long should registration be open?</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Quick Create Settings:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Format: Single Elimination</li>
                            <li>Entry Fee: Free</li>
                            <li>Prize Pool: $0</li>
                            <li>Status: Draft (you can edit later)</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('tournaments.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                        <div>
                            <a href="{{ url_for('tournaments.create') }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-cog"></i> Advanced Create
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
