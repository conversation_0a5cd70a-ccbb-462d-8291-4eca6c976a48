{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-user-plus"></i> Register Team
                </h3>
                <small class="text-muted">{{ tournament.name }}</small>
            </div>
            <div class="card-body">
                <!-- Tournament Info -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Tournament Details</h6>
                    <div class="row">
                        <div class="col-6">
                            <strong>Game:</strong> {{ tournament.game.title() }}<br>
                            <strong>Format:</strong> {{ tournament.format.value.replace('_', ' ').title() }}<br>
                            <strong>Team Size:</strong> {{ tournament.team_size }}v{{ tournament.team_size }}
                        </div>
                        <div class="col-6">
                            <strong>Entry Fee:</strong> ${{ tournament.entry_fee or 0 }}<br>
                            <strong>Prize Pool:</strong> ${{ tournament.prize_pool or 0 }}<br>
                            <strong>Spots Left:</strong> {{ tournament.max_teams - tournament.registered_teams_count }}
                        </div>
                    </div>
                </div>
                
                <!-- Registration Form -->
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.team_id.label(class="form-label") }}
                        {{ form.team_id(class="form-select" + (" is-invalid" if form.team_id.errors else "")) }}
                        {% if form.team_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.team_id.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">You can only register teams where you are the captain</div>
                    </div>
                    
                    <!-- Registration Requirements -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-clipboard-check"></i> Registration Requirements</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="agree-rules" required>
                                <label class="form-check-label" for="agree-rules">
                                    I agree to follow all tournament rules and regulations
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="agree-schedule" required>
                                <label class="form-check-label" for="agree-schedule">
                                    My team is available for the tournament schedule
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="agree-conduct" required>
                                <label class="form-check-label" for="agree-conduct">
                                    My team will maintain good sportsmanship and conduct
                                </label>
                            </div>
                            {% if tournament.entry_fee and tournament.entry_fee > 0 %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agree-payment" required>
                                <label class="form-check-label" for="agree-payment">
                                    I understand the entry fee of ${{ tournament.entry_fee }} must be paid
                                </label>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Tournament Schedule -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-calendar"></i> Important Dates</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Registration Ends</small><br>
                                    <strong>{{ tournament.registration_end.strftime('%B %d, %Y') }}</strong><br>
                                    <small>{{ tournament.registration_end.strftime('%I:%M %p') }}</small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Tournament Starts</small><br>
                                    <strong>{{ tournament.tournament_start.strftime('%B %d, %Y') }}</strong><br>
                                    <small>{{ tournament.tournament_start.strftime('%I:%M %p') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% if tournament.rules %}
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-gavel"></i> Tournament Rules</h6>
                        </div>
                        <div class="card-body">
                            <div style="max-height: 200px; overflow-y: auto;">
                                {{ tournament.rules|nl2br }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('tournaments.view', id=tournament.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Tournament
                        </a>
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Enable/disable submit button based on checkboxes
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][required]');
    const submitButton = document.querySelector('input[type="submit"]');
    
    function updateSubmitButton() {
        const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
        submitButton.disabled = !allChecked;
    }
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSubmitButton);
    });
    
    // Initial check
    updateSubmitButton();
});
</script>
{% endblock %}
