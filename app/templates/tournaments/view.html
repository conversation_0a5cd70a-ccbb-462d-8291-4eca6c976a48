{% extends "base.html" %}

{% block content %}
<div class="row" data-tournament-id="{{ tournament.id }}">
    <div class="col-lg-8">
        <!-- Tournament Header -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">{{ tournament.name }}</h2>
                    <small class="text-muted">
                        <i class="fas fa-user"></i> Organized by {{ tournament.organizer.first_name }} {{ tournament.organizer.last_name }}
                    </small>
                </div>
                <span id="tournament-status" class="badge fs-6
                    {% if tournament.status.value == 'draft' %}bg-secondary
                    {% elif tournament.status.value == 'registration_open' %}bg-success
                    {% elif tournament.status.value == 'registration_closed' %}bg-warning
                    {% elif tournament.status.value == 'in_progress' %}bg-primary
                    {% elif tournament.status.value == 'completed' %}bg-info
                    {% else %}bg-danger{% endif %}">
                    {% if tournament.status.value == 'in_progress' %}<span class="live-indicator"></span>{% endif %}
                    {{ tournament.status.value.replace('_', ' ').title() }}
                </span>
            </div>
            <div class="card-body">
                {% if tournament.description %}
                <p class="card-text">{{ tournament.description }}</p>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-gamepad fa-2x text-primary mb-2"></i>
                            <h5>{{ tournament.game.title() }}</h5>
                            <small class="text-muted">Game</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-sitemap fa-2x text-info mb-2"></i>
                            <h5>{{ tournament.format.value.replace('_', ' ').title() }}</h5>
                            <small class="text-muted">Format</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-users fa-2x text-warning mb-2"></i>
                            <h5>{{ tournament.team_size }}v{{ tournament.team_size }}</h5>
                            <small class="text-muted">Team Size</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-trophy fa-2x text-success mb-2"></i>
                            <h5>${{ tournament.prize_pool or 0 }}</h5>
                            <small class="text-muted">Prize Pool</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tournament Schedule -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar"></i> Schedule</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Registration Period</h6>
                        <p class="mb-1">
                            <i class="fas fa-play text-success"></i> 
                            <strong>Start:</strong> {{ tournament.registration_start.strftime('%B %d, %Y at %I:%M %p') }}
                        </p>
                        <p class="mb-3">
                            <i class="fas fa-stop text-danger"></i> 
                            <strong>End:</strong> {{ tournament.registration_end.strftime('%B %d, %Y at %I:%M %p') }}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>Tournament Period</h6>
                        <p class="mb-1">
                            <i class="fas fa-flag text-primary"></i> 
                            <strong>Start:</strong> {{ tournament.tournament_start.strftime('%B %d, %Y at %I:%M %p') }}
                        </p>
                        {% if tournament.tournament_end %}
                        <p class="mb-3">
                            <i class="fas fa-flag-checkered text-info"></i> 
                            <strong>End:</strong> {{ tournament.tournament_end.strftime('%B %d, %Y at %I:%M %p') }}
                        </p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tournament Rules -->
        {% if tournament.rules %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-gavel"></i> Rules & Regulations</h5>
            </div>
            <div class="card-body">
                <div class="rules-content">{{ tournament.rules|nl2br }}</div>
            </div>
        </div>
        {% endif %}
        
        <!-- Registered Teams -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> Registered Teams 
                    <span class="badge bg-primary">{{ registered_teams|length }}/{{ tournament.max_teams }}</span>
                </h5>
                {% if can_register %}
                <a href="{{ url_for('tournaments.register_team', id=tournament.id) }}" class="btn btn-success btn-sm">
                    <i class="fas fa-plus"></i> Register Team
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if registered_teams %}
                <div class="row">
                    {% for registration in registered_teams %}
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ registration.team.name }}</h6>
                                        <small class="text-muted">{{ registration.team.tag }}</small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i> 
                                            Registered {{ registration.registered_at.strftime('%m/%d/%Y') }}
                                        </small>
                                    </div>
                                    {% if can_manage %}
                                    <form method="POST" action="{{ url_for('tournaments.unregister_team', id=tournament.id, team_id=registration.team.id) }}" class="d-inline">
                                        <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                onclick="return confirm('Are you sure you want to unregister this team?')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No teams registered yet</h5>
                    <p class="text-muted">Be the first team to register for this tournament!</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Tournament Actions -->
        {% if can_manage %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cog"></i> Tournament Management</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('tournaments.edit', id=tournament.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Edit Tournament
                    </a>
                    
                    {% if tournament.status.value == 'draft' %}
                    <form method="POST" action="{{ url_for('tournaments.open_registration', id=tournament.id) }}">
                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-door-open"></i> Open Registration
                        </button>
                    </form>
                    {% elif tournament.status.value == 'registration_open' %}
                    <form method="POST" action="{{ url_for('tournaments.close_registration', id=tournament.id) }}">
                        <button type="submit" class="btn btn-warning w-100">
                            <i class="fas fa-door-closed"></i> Close Registration
                        </button>
                    </form>
                    {% elif tournament.status.value == 'registration_closed' %}
                    <form method="POST" action="{{ url_for('tournaments.start_tournament', id=tournament.id) }}">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-play"></i> Start Tournament
                        </button>
                    </form>
                    {% endif %}
                    
                    {% if tournament.status.value in ['draft', 'registration_open', 'registration_closed'] %}
                    <form method="POST" action="{{ url_for('tournaments.delete', id=tournament.id) }}">
                        <button type="submit" class="btn btn-outline-danger w-100" 
                                onclick="return confirm('Are you sure you want to delete this tournament?')">
                            <i class="fas fa-trash"></i> Delete Tournament
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Tournament Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Tournament Info</h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Entry Fee:</strong> ${{ tournament.entry_fee or 0 }}
                </div>
                <div class="mb-2">
                    <strong>Max Teams:</strong> {{ tournament.max_teams }}
                </div>
                <div class="mb-2">
                    <strong>Created:</strong> {{ tournament.created_at.strftime('%B %d, %Y') }}
                </div>
                {% if tournament.stream_url %}
                <div class="mb-2">
                    <strong>Stream:</strong> 
                    <a href="{{ tournament.stream_url }}" target="_blank" class="text-decoration-none">
                        <i class="fas fa-external-link-alt"></i> Watch Live
                    </a>
                </div>
                {% endif %}
                {% if tournament.discord_server %}
                <div class="mb-2">
                    <strong>Discord:</strong> 
                    <a href="{{ tournament.discord_server }}" target="_blank" class="text-decoration-none">
                        <i class="fab fa-discord"></i> Join Server
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Live Streams -->
        {% set active_streams = tournament.streams|selectattr('is_active')|list %}
        {% if active_streams %}
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-video text-danger"></i> Live Streams</h5>
                {% if current_user.is_authenticated and (current_user.role.name in ['ADMIN', 'ORGANIZER']) and (current_user.role.name == 'ADMIN' or tournament.organizer_id == current_user.id) %}
                <a href="{{ url_for('streams.add_tournament_stream', tournament_id=tournament.id) }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-plus"></i> Add Stream
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% for stream in active_streams %}
                <div class="stream-container mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">
                            <i class="fab fa-{{ stream.platform }}"></i> {{ stream.title }}
                            {% if stream.platform == 'twitch' %}
                            <span class="badge bg-purple">Twitch</span>
                            {% elif stream.platform == 'youtube' %}
                            <span class="badge bg-danger">YouTube</span>
                            {% endif %}
                        </h6>
                        {% if current_user.is_authenticated and (current_user.role.name in ['ADMIN', 'ORGANIZER']) and (current_user.role.name == 'ADMIN' or tournament.organizer_id == current_user.id) %}
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <form method="POST" action="{{ url_for('streams.deactivate_stream', stream_id=stream.id) }}" class="d-inline">
                                        <button type="submit" class="dropdown-item text-warning">
                                            <i class="fas fa-pause"></i> Deactivate
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="POST" action="{{ url_for('streams.delete_stream', stream_id=stream.id) }}" class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this stream?')">
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% if stream.description %}
                    <p class="text-muted small mb-2">{{ stream.description }}</p>
                    {% endif %}
                    <div class="ratio ratio-16x9">
                        {% if stream.platform == 'twitch' %}
                        <iframe
                            src="{{ stream.embed_url }}"
                            allowfullscreen="true"
                            scrolling="no"
                            frameborder="0">
                        </iframe>
                        {% elif stream.platform == 'youtube' %}
                        <iframe
                            src="{{ stream.embed_url }}"
                            title="{{ stream.title }}"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen>
                        </iframe>
                        {% else %}
                        <iframe
                            src="{{ stream.embed_url }}"
                            frameborder="0"
                            allowfullscreen>
                        </iframe>
                        {% endif %}
                    </div>
                    <div class="mt-2">
                        <a href="{{ stream.stream_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> Open in {{ stream.platform.title() }}
                        </a>
                    </div>
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
        {% elif current_user.is_authenticated and (current_user.role.name in ['ADMIN', 'ORGANIZER']) and (current_user.role.name == 'ADMIN' or tournament.organizer_id == current_user.id) %}
        <div class="card mb-4">
            <div class="card-body text-center">
                <i class="fas fa-video fa-3x text-muted mb-3"></i>
                <h5>No Live Streams</h5>
                <p class="text-muted">Add a live stream to engage your audience during the tournament.</p>
                <a href="{{ url_for('streams.add_tournament_stream', tournament_id=tournament.id) }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Stream
                </a>
            </div>
        </div>
        {% endif %}

        <!-- General Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-eye"></i> View</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if tournament.status.value in ['in_progress', 'completed'] %}
                    <a href="{{ url_for('tournaments.view_bracket', id=tournament.id) }}" class="btn btn-primary">
                        <i class="fas fa-sitemap"></i> View Bracket
                    </a>
                    {% endif %}

                    <a href="{{ url_for('matches.index', tournament_id=tournament.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-gamepad"></i> View Matches
                    </a>

                    {% if tournament.status.value in ['in_progress', 'completed'] %}
                    <a href="{{ url_for('leaderboards.tournament_leaderboard', tournament_id=tournament.id) }}" class="btn btn-outline-success">
                        <i class="fas fa-list-ol"></i> View Leaderboard
                    </a>
                    {% endif %}

                    {% if tournament.registered_teams_count > 0 %}
                    <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#teamsModal">
                        <i class="fas fa-users"></i> View Teams ({{ tournament.registered_teams_count }})
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Registration Status -->
        {% if tournament.is_registration_open %}
        <div class="alert alert-success">
            <i class="fas fa-door-open"></i>
            <strong>Registration is Open!</strong><br>
            Teams can register until {{ tournament.registration_end.strftime('%B %d, %Y at %I:%M %p') }}
        </div>
        {% elif tournament.status.value == 'registration_closed' %}
        <div class="alert alert-warning">
            <i class="fas fa-door-closed"></i>
            <strong>Registration Closed</strong><br>
            Tournament starts {{ tournament.tournament_start.strftime('%B %d, %Y at %I:%M %p') }}
        </div>
        {% elif tournament.status.value == 'in_progress' %}
        <div class="alert alert-primary">
            <i class="fas fa-play"></i>
            <strong>Tournament In Progress</strong><br>
            Good luck to all participants!
        </div>
        {% elif tournament.status.value == 'completed' %}
        <div class="alert alert-info">
            <i class="fas fa-flag-checkered"></i>
            <strong>Tournament Completed</strong><br>
            Congratulations to all participants!
        </div>
        {% endif %}
    </div>
</div>

<!-- Teams Modal -->
<div class="modal fade" id="teamsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Registered Teams</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                {% if tournament.tournament_teams %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Team</th>
                                <th>Tag</th>
                                <th>Seed</th>
                                <th>Registered</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for tournament_team in tournament.tournament_teams %}
                            {% if tournament_team.is_active %}
                            <tr>
                                <td>
                                    <strong>{{ tournament_team.team.name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ tournament_team.team.tag }}</span>
                                </td>
                                <td>
                                    {{ tournament_team.seed or 'TBD' }}
                                </td>
                                <td>
                                    {{ tournament_team.registered_at.strftime('%m/%d/%Y') }}
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">No teams registered yet.</p>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
