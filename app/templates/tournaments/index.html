{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-trophy"></i> Tournaments</h1>
    {% if current_user.is_authenticated and current_user.role.value in ['organizer', 'admin'] %}
    <div class="btn-group">
        <a href="{{ url_for('tournaments.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Tournament
        </a>
        <a href="{{ url_for('tournaments.quick_create') }}" class="btn btn-outline-primary">
            <i class="fas fa-bolt"></i> Quick Create
        </a>
    </div>
    {% endif %}
</div>

<!-- Enhanced Search and Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-filter"></i> Search & Filter Tournaments</h6>
    </div>
    <div class="card-body">
        <!-- Search Bar -->
        <div class="row mb-3">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="tournamentSearch"
                           placeholder="Search tournaments by name, game, or organizer..."
                           data-search=".tournament-card">
                </div>
            </div>
            <div class="col-md-4">
                <div class="btn-group w-100" role="group">
                    <input type="radio" class="btn-check" name="viewMode" id="gridView" checked>
                    <label class="btn btn-outline-primary" for="gridView">
                        <i class="fas fa-th"></i> Grid
                    </label>
                    <input type="radio" class="btn-check" name="viewMode" id="listView">
                    <label class="btn btn-outline-primary" for="listView">
                        <i class="fas fa-list"></i> List
                    </label>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Statuses</option>
                    <option value="draft" {% if status_filter == 'draft' %}selected{% endif %}>Draft</option>
                    <option value="registration_open" {% if status_filter == 'registration_open' %}selected{% endif %}>Registration Open</option>
                    <option value="registration_closed" {% if status_filter == 'registration_closed' %}selected{% endif %}>Registration Closed</option>
                    <option value="in_progress" {% if status_filter == 'in_progress' %}selected{% endif %}>In Progress</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="game" class="form-label">Game</label>
                <select name="game" id="game" class="form-select">
                    <option value="all" {% if game_filter == 'all' %}selected{% endif %}>All Games</option>
                    {% for game in games %}
                    <option value="{{ game }}" {% if game_filter == game %}selected{% endif %}>{{ game.title() }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-filter"></i> Filter
                </button>
                <a href="{{ url_for('tournaments.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Tournament Grid -->
{% if tournaments.items %}
<div class="row">
    {% for tournament in tournaments.items %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ tournament.name }}</h5>
                <span class="badge 
                    {% if tournament.status.value == 'draft' %}bg-secondary
                    {% elif tournament.status.value == 'registration_open' %}bg-success
                    {% elif tournament.status.value == 'registration_closed' %}bg-warning
                    {% elif tournament.status.value == 'in_progress' %}bg-primary
                    {% elif tournament.status.value == 'completed' %}bg-info
                    {% else %}bg-danger{% endif %}">
                    {{ tournament.status.value.replace('_', ' ').title() }}
                </span>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-gamepad"></i> {{ tournament.game.title() }}
                        <span class="ms-2">
                            <i class="fas fa-users"></i> {{ tournament.registered_teams_count }}/{{ tournament.max_teams }} teams
                        </span>
                    </small>
                </div>
                
                {% if tournament.description %}
                <p class="card-text">{{ tournament.description[:100] }}{% if tournament.description|length > 100 %}...{% endif %}</p>
                {% endif %}
                
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <small class="text-muted">Format</small><br>
                        <strong>{{ tournament.format.value.replace('_', ' ').title() }}</strong>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Team Size</small><br>
                        <strong>{{ tournament.team_size }}v{{ tournament.team_size }}</strong>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Prize Pool</small><br>
                        <strong>${{ tournament.prize_pool or 0 }}</strong>
                    </div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        Registration: {{ tournament.registration_start.strftime('%m/%d') }} - {{ tournament.registration_end.strftime('%m/%d') }}
                    </small>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-play"></i> 
                        Tournament: {{ tournament.tournament_start.strftime('%m/%d/%Y %I:%M %p') }}
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{{ url_for('tournaments.view', id=tournament.id) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    {% if tournament.is_registration_open %}
                    <span class="badge bg-success">
                        <i class="fas fa-door-open"></i> Open for Registration
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if tournaments.pages > 1 %}
<nav aria-label="Tournament pagination">
    <ul class="pagination justify-content-center">
        {% if tournaments.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('tournaments.index', page=tournaments.prev_num, status=status_filter, game=game_filter) }}">
                <i class="fas fa-chevron-left"></i> Previous
            </a>
        </li>
        {% endif %}
        
        {% for page_num in tournaments.iter_pages() %}
            {% if page_num %}
                {% if page_num != tournaments.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('tournaments.index', page=page_num, status=status_filter, game=game_filter) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if tournaments.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('tournaments.index', page=tournaments.next_num, status=status_filter, game=game_filter) }}">
                Next <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No tournaments found</h3>
    <p class="text-muted">Be the first to create a tournament!</p>
    {% if current_user.is_authenticated and current_user.role.value in ['organizer', 'admin'] %}
    <a href="{{ url_for('tournaments.create') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Create Tournament
    </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced search functionality
    const searchInput = document.getElementById('tournamentSearch');
    const tournamentCards = document.querySelectorAll('.tournament-card');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();

            tournamentCards.forEach(card => {
                const cardText = card.textContent.toLowerCase();
                const cardElement = card.closest('.col-lg-4, .col-md-6');

                if (cardText.includes(query)) {
                    cardElement.style.display = '';
                    card.classList.add('fade-in');
                } else {
                    cardElement.style.display = 'none';
                    card.classList.remove('fade-in');
                }
            });

            // Show/hide no results message
            const visibleCards = Array.from(tournamentCards).filter(card =>
                card.closest('.col-lg-4, .col-md-6').style.display !== 'none'
            );

            const noResultsMsg = document.getElementById('noResultsMessage');
            if (visibleCards.length === 0 && query.length > 0) {
                if (!noResultsMsg) {
                    const msg = document.createElement('div');
                    msg.id = 'noResultsMessage';
                    msg.className = 'text-center py-5';
                    msg.innerHTML = `
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3 class="text-muted">No tournaments found</h3>
                        <p class="text-muted">Try adjusting your search terms or filters.</p>
                    `;
                    document.querySelector('.row').appendChild(msg);
                }
            } else if (noResultsMsg) {
                noResultsMsg.remove();
            }
        });
    }

    // View mode switching
    const gridViewBtn = document.getElementById('gridView');
    const listViewBtn = document.getElementById('listView');
    const tournamentContainer = document.querySelector('.row');

    if (gridViewBtn && listViewBtn) {
        gridViewBtn.addEventListener('change', function() {
            if (this.checked) {
                tournamentContainer.className = 'row';
                tournamentCards.forEach(card => {
                    const col = card.closest('.col-lg-4, .col-md-6');
                    if (col) {
                        col.className = 'col-lg-4 col-md-6 mb-4';
                    }
                });
            }
        });

        listViewBtn.addEventListener('change', function() {
            if (this.checked) {
                tournamentContainer.className = 'row';
                tournamentCards.forEach(card => {
                    const col = card.closest('.col-lg-4, .col-md-6');
                    if (col) {
                        col.className = 'col-12 mb-3';
                        card.classList.add('tournament-list-view');
                    }
                });
            }
        });
    }

    // Save filters functionality
    const saveFiltersBtn = document.getElementById('saveFilters');
    if (saveFiltersBtn) {
        saveFiltersBtn.addEventListener('click', function() {
            const filters = {
                status: document.getElementById('status').value,
                game: document.getElementById('game').value,
                search: searchInput.value
            };

            localStorage.setItem('tournamentFilters', JSON.stringify(filters));
            EsportsApp.showToast('Filters saved successfully!', 'success');
        });

        // Load saved filters
        const savedFilters = localStorage.getItem('tournamentFilters');
        if (savedFilters) {
            const filters = JSON.parse(savedFilters);
            if (filters.search) searchInput.value = filters.search;
        }
    }
});
</script>

<style>
.tournament-list-view {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
}

.tournament-list-view .card-body {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tournament-list-view .card-footer {
    border-left: 1px solid #dee2e6;
    border-bottom: none;
    display: flex;
    align-items: center;
    padding: 1rem;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
{% endblock %}
