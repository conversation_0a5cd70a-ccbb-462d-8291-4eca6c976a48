{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-users"></i> My Teams</h1>
    <div class="btn-group">
        <a href="{{ url_for('teams.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Team
        </a>
        <a href="{{ url_for('teams.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-search"></i> Browse Teams
        </a>
    </div>
</div>

<!-- Team Membership -->
{% if member_team %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-user-friends"></i> Current Team Membership</h5>
    </div>
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h6 class="mb-1">{{ member_team.name }}</h6>
                <p class="text-muted mb-1">{{ member_team.tag }}</p>
                {% if member_team.description %}
                    <p class="mb-2">{{ member_team.description }}</p>
                {% endif %}
                <small class="text-muted">
                    <i class="fas fa-calendar"></i> Joined {{ member_team.created_at.strftime('%B %d, %Y') }}
                </small>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ url_for('teams.view', id=member_team.id) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-eye"></i> View Team
                </a>
                {% if member_team.captain_id != current_user.player_profile.id %}
                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="confirmLeaveTeam()">
                        <i class="fas fa-sign-out-alt"></i> Leave Team
                    </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Teams I Captain -->
{% if captain_teams %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-crown"></i> Teams I Captain ({{ captain_teams|length }})</h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for team in captain_teams %}
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">{{ team.name }}</h6>
                            <span class="badge bg-primary">Captain</span>
                        </div>
                        <p class="text-muted small mb-1">{{ team.tag }}</p>
                        {% if team.description %}
                            <p class="card-text small">{{ team.description[:100] }}{% if team.description|length > 100 %}...{% endif %}</p>
                        {% endif %}
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-users"></i> {{ team.active_players_count }} players
                            </small>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100">
                            <a href="{{ url_for('teams.view', id=team.id) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="#" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- No Teams Message -->
{% if not member_team and not captain_teams %}
<div class="text-center py-5">
    <i class="fas fa-users fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No Team Memberships</h3>
    <p class="text-muted">You're not currently a member or captain of any teams.</p>
    <div class="mt-3">
        <a href="{{ url_for('teams.create') }}" class="btn btn-primary me-2">
            <i class="fas fa-plus"></i> Create a Team
        </a>
        <a href="{{ url_for('teams.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-search"></i> Find a Team
        </a>
    </div>
</div>
{% endif %}

<!-- Tournament Registrations -->
{% if captain_teams %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-trophy"></i> Tournament Registrations</h5>
    </div>
    <div class="card-body">
        <p class="text-muted">Tournament registration information for your teams will be displayed here.</p>
        <a href="{{ url_for('tournaments.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-trophy"></i> Browse Tournaments
        </a>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function confirmLeaveTeam() {
    if (confirm('Are you sure you want to leave this team? This action cannot be undone.')) {
        // TODO: Implement leave team functionality
        alert('Leave team functionality will be implemented soon.');
    }
}
</script>
{% endblock %}
