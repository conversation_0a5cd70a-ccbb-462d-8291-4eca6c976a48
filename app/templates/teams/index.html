{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-users"></i> Teams</h1>
    {% if current_user.is_authenticated and current_user.role.value == 'player' %}
    <div class="btn-group">
        <a href="{{ url_for('teams.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Team
        </a>
        <a href="{{ url_for('teams.my_teams') }}" class="btn btn-outline-primary">
            <i class="fas fa-user"></i> My Teams
        </a>
    </div>
    {% endif %}
</div>

<!-- Search and Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-12">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" name="search"
                           placeholder="Search teams by name, tag, or description..."
                           value="{{ search }}">
                </div>
            </div>

            <!-- Advanced Filters -->
            <div class="col-md-3">
                <select class="form-select" name="sort">
                    <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Sort by Name</option>
                    <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>Sort by Date Created</option>
                    <option value="players_count" {% if sort_by == 'players_count' %}selected{% endif %}>Sort by Team Size</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="order">
                    <option value="asc" {% if order == 'asc' %}selected{% endif %}>Ascending</option>
                    <option value="desc" {% if order == 'desc' %}selected{% endif %}>Descending</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="number" class="form-control" name="min_players"
                       placeholder="Min players" min="0" max="20"
                       value="{{ min_players if min_players }}">
            </div>
            <div class="col-md-2">
                <input type="number" class="form-control" name="max_players"
                       placeholder="Max players" min="0" max="20"
                       value="{{ max_players if max_players }}">
            </div>
            <div class="col-md-2">
                <select class="form-select" name="has_captain">
                    <option value="">Any Captain Status</option>
                    <option value="yes" {% if has_captain == 'yes' %}selected{% endif %}>Has Captain</option>
                    <option value="no" {% if has_captain == 'no' %}selected{% endif %}>No Captain</option>
                </select>
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </form>

        <!-- Active Filters Display -->
        {% if search or min_players or max_players or has_captain %}
        <div class="mt-3">
            <small class="text-muted">Active filters:</small>
            <div class="d-flex flex-wrap gap-2 mt-1">
                {% if search %}
                <span class="badge bg-light text-dark">
                    Search: "{{ search }}"
                    <a href="{{ url_for('teams.index', sort=sort_by, order=order, min_players=min_players, max_players=max_players, has_captain=has_captain) }}"
                       class="text-decoration-none ms-1">×</a>
                </span>
                {% endif %}
                {% if min_players %}
                <span class="badge bg-light text-dark">
                    Min players: {{ min_players }}
                    <a href="{{ url_for('teams.index', search=search, sort=sort_by, order=order, max_players=max_players, has_captain=has_captain) }}"
                       class="text-decoration-none ms-1">×</a>
                </span>
                {% endif %}
                {% if max_players %}
                <span class="badge bg-light text-dark">
                    Max players: {{ max_players }}
                    <a href="{{ url_for('teams.index', search=search, sort=sort_by, order=order, min_players=min_players, has_captain=has_captain) }}"
                       class="text-decoration-none ms-1">×</a>
                </span>
                {% endif %}
                {% if has_captain %}
                <span class="badge bg-light text-dark">
                    Captain: {{ 'Yes' if has_captain == 'yes' else 'No' }}
                    <a href="{{ url_for('teams.index', search=search, sort=sort_by, order=order, min_players=min_players, max_players=max_players) }}"
                       class="text-decoration-none ms-1">×</a>
                </span>
                {% endif %}
                <a href="{{ url_for('teams.index') }}" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear All
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Teams Grid -->
{% if teams.items %}
<div class="row">
    {% for team in teams.items %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100 team-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="card-title mb-1">{{ team.name }}</h5>
                        <span class="badge bg-secondary">{{ team.tag }}</span>
                    </div>
                    {% if team.logo_url %}
                    <img src="{{ team.logo_url }}" alt="{{ team.name }} logo" 
                         class="team-logo rounded" width="40" height="40">
                    {% else %}
                    <div class="team-logo-placeholder rounded d-flex align-items-center justify-content-center">
                        <i class="fas fa-users text-muted"></i>
                    </div>
                    {% endif %}
                </div>
                
                {% if team.description %}
                <p class="card-text text-muted small mb-3">
                    {{ team.description[:120] }}{% if team.description|length > 120 %}...{% endif %}
                </p>
                {% endif %}
                
                <div class="team-stats mb-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number">{{ team.active_players_count }}</div>
                                <div class="stat-label">Players</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number">{{ team.tournament_registrations.count() }}</div>
                                <div class="stat-label">Tournaments</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number">
                                    {% if team.captain %}
                                        <i class="fas fa-crown text-warning" title="Captain: {{ team.captain.in_game_name }}"></i>
                                    {% else %}
                                        <i class="fas fa-user-slash text-muted" title="No Captain"></i>
                                    {% endif %}
                                </div>
                                <div class="stat-label">Captain</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="team-meta">
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i> Created {{ team.created_at.strftime('%b %d, %Y') }}
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{{ url_for('teams.view', id=team.id) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    {% if current_user.is_authenticated and current_user.role.value == 'player' and current_user.player_profile %}
                        {% if current_user.player_profile.team_id != team.id %}
                        <button class="btn btn-outline-success btn-sm" onclick="requestToJoin({{ team.id }})">
                            <i class="fas fa-user-plus"></i> Request to Join
                        </button>
                        {% else %}
                        <span class="badge bg-success">Your Team</span>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if teams.pages > 1 %}
<nav aria-label="Teams pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if teams.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('teams.index', page=teams.prev_num, search=search, sort=sort_by, order=order, min_players=min_players, max_players=max_players, has_captain=has_captain) }}">
                <i class="fas fa-chevron-left"></i> Previous
            </a>
        </li>
        {% endif %}

        {% for page_num in teams.iter_pages() %}
            {% if page_num %}
                {% if page_num != teams.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('teams.index', page=page_num, search=search, sort=sort_by, order=order, min_players=min_players, max_players=max_players, has_captain=has_captain) }}">
                        {{ page_num }}
                    </a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}

        {% if teams.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('teams.index', page=teams.next_num, search=search, sort=sort_by, order=order, min_players=min_players, max_players=max_players, has_captain=has_captain) }}">
                Next <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- No Teams Found -->
<div class="text-center py-5">
    <i class="fas fa-users fa-3x text-muted mb-3"></i>
    {% if search %}
    <h3 class="text-muted">No Teams Found</h3>
    <p class="text-muted">No teams match your search criteria "{{ search }}".</p>
    <a href="{{ url_for('teams.index') }}" class="btn btn-outline-primary">
        <i class="fas fa-times"></i> Clear Search
    </a>
    {% else %}
    <h3 class="text-muted">No Teams Yet</h3>
    <p class="text-muted">Be the first to create a team and start competing!</p>
    {% if current_user.is_authenticated and current_user.role.value == 'player' %}
    <a href="{{ url_for('teams.create') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Create First Team
    </a>
    {% endif %}
    {% endif %}
</div>
{% endif %}

<!-- Results Summary -->
{% if teams.items %}
<div class="mt-4 text-center">
    <small class="text-muted">
        Showing {{ teams.per_page * (teams.page - 1) + 1 }} to 
        {{ teams.per_page * (teams.page - 1) + teams.items|length }} of 
        {{ teams.total }} teams
    </small>
</div>
{% endif %}
{% endblock %}

{% block styles %}
<style>
.team-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.team-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.team-logo-placeholder {
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.stat-item {
    padding: 0.5rem 0;
}

.stat-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.team-meta {
    border-top: 1px solid #f8f9fa;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function requestToJoin(teamId) {
    // TODO: Implement request to join functionality
    alert('Request to join functionality will be implemented soon.');
}
</script>
{% endblock %}
