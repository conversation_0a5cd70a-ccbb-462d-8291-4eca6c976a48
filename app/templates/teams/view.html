{% extends "base.html" %}

{% block content %}
<!-- Team Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="d-flex align-items-center mb-3">
            {% if team.logo_url %}
            <img src="{{ team.logo_url }}" alt="{{ team.name }} logo" 
                 class="team-logo-large rounded me-3" width="80" height="80">
            {% else %}
            <div class="team-logo-large-placeholder rounded me-3 d-flex align-items-center justify-content-center">
                <i class="fas fa-users fa-2x text-muted"></i>
            </div>
            {% endif %}
            <div>
                <h1 class="mb-1">{{ team.name }}</h1>
                <span class="badge bg-primary fs-6">{{ team.tag }}</span>
                {% if team.captain %}
                <span class="badge bg-warning text-dark ms-2">
                    <i class="fas fa-crown"></i> Captain: {{ team.captain.in_game_name }}
                </span>
                {% endif %}
            </div>
        </div>
        
        {% if team.description %}
        <p class="lead text-muted">{{ team.description }}</p>
        {% endif %}
        
        <div class="team-meta">
            <small class="text-muted">
                <i class="fas fa-calendar"></i> Created {{ team.created_at.strftime('%B %d, %Y') }}
                {% if team.updated_at != team.created_at %}
                • Last updated {{ team.updated_at.strftime('%B %d, %Y') }}
                {% endif %}
            </small>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Team Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-number text-primary">{{ members|length }}</div>
                            <div class="stat-label">Active Players</div>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-number text-info">{{ team_stats.total_tournaments }}</div>
                            <div class="stat-label">Tournaments</div>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-number text-success">{{ team_stats.total_wins }}</div>
                            <div class="stat-label">Wins</div>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-number text-danger">{{ team_stats.total_losses }}</div>
                            <div class="stat-label">Losses</div>
                        </div>
                    </div>
                    {% if team_stats.total_draws > 0 %}
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number text-warning">{{ team_stats.total_draws }}</div>
                            <div class="stat-label">Draws</div>
                        </div>
                    </div>
                    {% endif %}
                    {% if team_stats.tournament_placements %}
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number text-warning">
                                <i class="fas fa-medal"></i> #{{ team_stats.tournament_placements[0].placement }}
                            </div>
                            <div class="stat-label">Best Placement</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% if team_stats.total_matches > 0 %}
                <div class="mt-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">Win Rate</small>
                        <small class="fw-bold">{{ team_stats.win_rate }}%</small>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ team_stats.win_rate }}%" 
                             aria-valuenow="{{ team_stats.win_rate }}" 
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="mb-4">
    <div class="btn-group">
        <a href="{{ url_for('teams.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Teams
        </a>
        {% if current_user.is_authenticated and current_user.role.value == 'player' and current_user.player_profile %}
            {% if current_user.player_profile.id == team.captain_id %}
            <a href="#" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Team
            </a>
            <button class="btn btn-success" onclick="manageMembers()">
                <i class="fas fa-users-cog"></i> Manage Members
            </button>
            {% elif current_user.player_profile.team_id != team.id %}
            <button class="btn btn-outline-success" onclick="requestToJoin({{ team.id }})">
                <i class="fas fa-user-plus"></i> Request to Join
            </button>
            {% endif %}
        {% endif %}
    </div>
</div>

<!-- Team Members -->
<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users"></i> Team Members ({{ members|length }})</h5>
            </div>
            <div class="card-body">
                {% if members %}
                <div class="row">
                    {% for member in members %}
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded">
                            {% if member.avatar_url %}
                            <img src="{{ member.avatar_url }}" alt="{{ member.in_game_name }}"
                                 class="rounded-circle me-3" width="50" height="50">
                            {% else %}
                            <div class="avatar-placeholder rounded-circle me-3 d-flex align-items-center justify-content-center">
                                <i class="fas fa-user text-muted"></i>
                            </div>
                            {% endif %}
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    {{ member.in_game_name }}
                                    {% if member.is_captain %}
                                    <i class="fas fa-crown text-warning ms-1" title="Team Captain"></i>
                                    {% endif %}
                                </h6>
                                <p class="text-muted mb-1 small">{{ member.full_name }}</p>
                                {% if member.skill_level %}
                                <span class="badge bg-light text-dark">{{ member.skill_level }}</span>
                                {% endif %}
                            </div>
                            <div class="text-end">
                                <small class="text-muted d-block">{{ member.matches_played }} matches</small>
                                {% if member.matches_played > 0 %}
                                <small class="text-success">{{ member.win_rate|round(1) }}% win rate</small>
                                {% endif %}
                                {% if member.tournaments_won > 0 %}
                                <small class="text-warning d-block">
                                    <i class="fas fa-trophy"></i> {{ member.tournaments_won }} wins
                                </small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-user-slash fa-2x text-muted mb-3"></i>
                    <p class="text-muted">No active members in this team.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Recent Matches -->
        {% if team_stats.recent_matches %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-history"></i> Recent Matches</h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for match in team_stats.recent_matches %}
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center">
                                    {% if match.team_won %}
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    {% elif match.is_draw %}
                                    <i class="fas fa-minus-circle text-warning me-2"></i>
                                    {% else %}
                                    <i class="fas fa-times-circle text-danger me-2"></i>
                                    {% endif %}
                                    <div>
                                        <small class="fw-bold">vs {{ match.opponent_name }}</small>
                                        <br>
                                        <small class="text-muted">{{ match.tournament_name }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="fw-bold">{{ match.team_score }}-{{ match.opponent_score }}</small>
                                <br>
                                <small class="text-muted">{{ match.completed_at.strftime('%m/%d') }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Tournament History -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-trophy"></i> Tournament History</h6>
            </div>
            <div class="card-body">
                {% if tournament_registrations %}
                <div class="list-group list-group-flush">
                    {% for registration in tournament_registrations[:5] %}
                    <div class="list-group-item px-0">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ registration.tournament.name }}</h6>
                                <p class="mb-1 small text-muted">{{ registration.tournament.game }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> {{ registration.registered_at.strftime('%b %d, %Y') }}
                                </small>
                            </div>
                            <div class="text-end">
                                {% if registration.placement %}
                                <span class="badge bg-success">#{{ registration.placement }}</span>
                                {% else %}
                                <span class="badge bg-secondary">Active</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if tournament_registrations|length > 5 %}
                <div class="text-center mt-3">
                    <small class="text-muted">And {{ tournament_registrations|length - 5 }} more tournaments...</small>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-trophy fa-2x text-muted mb-2"></i>
                    <p class="text-muted small">No tournament history yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.team-logo-large-placeholder {
    width: 80px;
    height: 80px;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.stat-item {
    padding: 0.5rem 0;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

.team-meta {
    border-top: 1px solid #f8f9fa;
    padding-top: 1rem;
    margin-top: 1rem;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function requestToJoin(teamId) {
    // TODO: Implement request to join functionality
    alert('Request to join functionality will be implemented soon.');
}

function manageMembers() {
    // TODO: Implement member management functionality
    alert('Member management functionality will be implemented soon.');
}
</script>
{% endblock %}
