{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1><i class="fas fa-video"></i> Add Match Stream</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('matches.index') }}">Matches</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('tournaments.view', id=match.tournament.id) }}">{{ match.tournament.name }}</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('matches.view', id=match.id) }}">{{ match.teams_display }}</a></li>
                <li class="breadcrumb-item active">Add Stream</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus"></i> Add Stream for {{ match.teams_display }}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Match Details:</strong> {{ match.teams_display }} - Round {{ match.round_number }}, Match {{ match.match_number }}
                </div>
                
                <form method="POST" id="streamForm">
                    <div class="mb-3">
                        <label for="stream_url" class="form-label">Stream URL <span class="text-danger">*</span></label>
                        <input type="url" class="form-control" id="stream_url" name="stream_url" required
                               placeholder="https://twitch.tv/channel or https://youtube.com/watch?v=...">
                        <div class="form-text">
                            Supported platforms: Twitch, YouTube
                        </div>
                        <div id="url-validation" class="mt-2"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Stream Title</label>
                        <input type="text" class="form-control" id="title" name="title" 
                               placeholder="Leave empty to use default title"
                               value="{{ match.teams_display }} - Live Stream">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Optional description for the stream">Round {{ match.round_number }}, Match {{ match.match_number }} of {{ match.tournament.name }}</textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Stream
                        </button>
                        <a href="{{ url_for('matches.view', id=match.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Match
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Stream Guidelines</h6>
            </div>
            <div class="card-body">
                <h6>Supported Platforms:</h6>
                <ul class="list-unstyled">
                    <li><i class="fab fa-twitch text-purple"></i> <strong>Twitch</strong></li>
                    <li class="small text-muted">https://twitch.tv/channelname</li>
                    <li class="mt-2"><i class="fab fa-youtube text-danger"></i> <strong>YouTube</strong></li>
                    <li class="small text-muted">https://youtube.com/watch?v=VIDEO_ID</li>
                    <li class="small text-muted">https://youtu.be/VIDEO_ID</li>
                </ul>
                
                <hr>
                
                <h6>Tips:</h6>
                <ul class="small">
                    <li>Match streams will be displayed on the match page</li>
                    <li>You can add multiple streams for different perspectives</li>
                    <li>Streams can be deactivated or removed later</li>
                    <li>Make sure your stream is public and embeddable</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const streamUrlInput = document.getElementById('stream_url');
    const validationDiv = document.getElementById('url-validation');
    const submitBtn = document.querySelector('button[type="submit"]');
    
    let validationTimeout;
    
    streamUrlInput.addEventListener('input', function() {
        clearTimeout(validationTimeout);
        const url = this.value.trim();
        
        if (!url) {
            validationDiv.innerHTML = '';
            return;
        }
        
        validationDiv.innerHTML = '<div class="text-info"><i class="fas fa-spinner fa-spin"></i> Validating...</div>';
        
        validationTimeout = setTimeout(() => {
            validateStreamUrl(url);
        }, 500);
    });
    
    function validateStreamUrl(url) {
        fetch('/streams/api/validate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url: url })
        })
        .then(response => response.json())
        .then(data => {
            if (data.valid) {
                validationDiv.innerHTML = `
                    <div class="alert alert-success py-2">
                        <i class="fas fa-check"></i> Valid ${data.platform} stream detected
                        ${data.error ? '<br><small class="text-warning">' + data.error + '</small>' : ''}
                    </div>
                `;
            } else {
                validationDiv.innerHTML = `
                    <div class="alert alert-danger py-2">
                        <i class="fas fa-times"></i> ${data.error || 'Invalid stream URL'}
                    </div>
                `;
            }
        })
        .catch(error => {
            validationDiv.innerHTML = `
                <div class="alert alert-warning py-2">
                    <i class="fas fa-exclamation-triangle"></i> Could not validate URL
                </div>
            `;
        });
    }
});
</script>
{% endblock %}
