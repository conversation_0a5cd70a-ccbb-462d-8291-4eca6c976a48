{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-gamepad"></i> Matches</h1>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="tournament_id" class="form-label">Tournament</label>
                <select name="tournament_id" id="tournament_id" class="form-select">
                    <option value="">All Tournaments</option>
                    {% for tournament in tournaments %}
                    <option value="{{ tournament.id }}" 
                        {% if current_tournament_id == tournament.id %}selected{% endif %}>
                        {{ tournament.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="scheduled" {% if current_status == 'scheduled' %}selected{% endif %}>Scheduled</option>
                    <option value="in_progress" {% if current_status == 'in_progress' %}selected{% endif %}>In Progress</option>
                    <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>Completed</option>
                    <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                    <option value="disputed" {% if current_status == 'disputed' %}selected{% endif %}>Disputed</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter"></i> Filter
                </button>
                <a href="{{ url_for('matches.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

{% if matches.items %}
<div class="row">
    {% for match in matches.items %}
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-trophy"></i> {{ match.tournament.name }}
                </h6>
                <span class="badge 
                    {% if match.status.value == 'scheduled' %}bg-primary
                    {% elif match.status.value == 'in_progress' %}bg-warning
                    {% elif match.status.value == 'completed' %}bg-success
                    {% elif match.status.value == 'cancelled' %}bg-secondary
                    {% else %}bg-danger{% endif %}">
                    {{ match.status.value.replace('_', ' ').title() }}
                </span>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h5 class="mb-1">{{ match.teams_display }}</h5>
                    <small class="text-muted">
                        {% if match.bracket_position %}
                            {{ match.bracket_position }} - 
                        {% endif %}
                        Round {{ match.round_number }}, Match {{ match.match_number }}
                    </small>
                </div>
                
                {% if match.status.value == 'completed' %}
                <div class="text-center mb-3">
                    <div class="row">
                        <div class="col-5 text-end">
                            <strong class="{% if match.winner_id == match.team1_id %}text-success{% endif %}">
                                {{ match.team1_score }}
                            </strong>
                        </div>
                        <div class="col-2 text-center">
                            <span class="text-muted">-</span>
                        </div>
                        <div class="col-5 text-start">
                            <strong class="{% if match.winner_id == match.team2_id %}text-success{% endif %}">
                                {{ match.team2_score }}
                            </strong>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Format</small><br>
                        <strong>Best of {{ match.best_of }}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Game</small><br>
                        <strong>{{ match.tournament.game.title() }}</strong>
                    </div>
                </div>
                
                {% if match.scheduled_time %}
                <div class="mb-3">
                    <small class="text-muted">Scheduled Time</small><br>
                    <strong>
                        <i class="fas fa-calendar"></i> 
                        {{ match.scheduled_time.strftime('%B %d, %Y at %I:%M %p') }}
                    </strong>
                </div>
                {% endif %}
                
                {% if match.started_at %}
                <div class="mb-3">
                    <small class="text-muted">Started</small><br>
                    <strong>
                        <i class="fas fa-play"></i> 
                        {{ match.started_at.strftime('%B %d, %Y at %I:%M %p') }}
                    </strong>
                </div>
                {% endif %}
                
                {% if match.completed_at %}
                <div class="mb-3">
                    <small class="text-muted">Completed</small><br>
                    <strong>
                        <i class="fas fa-flag-checkered"></i> 
                        {{ match.completed_at.strftime('%B %d, %Y at %I:%M %p') }}
                    </strong>
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{{ url_for('matches.view', id=match.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    
                    {% if match.status.value == 'in_progress' %}
                    <span class="badge bg-warning">
                        <i class="fas fa-play"></i> Live
                    </span>
                    {% elif match.status.value == 'scheduled' %}
                    <span class="badge bg-primary">
                        <i class="fas fa-clock"></i> Upcoming
                    </span>
                    {% elif match.status.value == 'completed' and match.winner %}
                    <span class="badge bg-success">
                        <i class="fas fa-trophy"></i> {{ match.winner.team.name }} Won
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if matches.pages > 1 %}
<nav aria-label="Matches pagination">
    <ul class="pagination justify-content-center">
        {% if matches.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('matches.index', page=matches.prev_num, tournament_id=current_tournament_id, status=current_status) }}">
                <i class="fas fa-chevron-left"></i> Previous
            </a>
        </li>
        {% endif %}
        
        {% for page_num in matches.iter_pages() %}
            {% if page_num %}
                {% if page_num != matches.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('matches.index', page=page_num, tournament_id=current_tournament_id, status=current_status) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">...</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if matches.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('matches.index', page=matches.next_num, tournament_id=current_tournament_id, status=current_status) }}">
                Next <i class="fas fa-chevron-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <i class="fas fa-gamepad fa-3x text-muted mb-3"></i>
    <h3 class="text-muted">No matches found</h3>
    <p class="text-muted">
        {% if current_tournament_id or current_status %}
            Try adjusting your filters to see more matches.
        {% else %}
            Matches will appear here once tournaments are started.
        {% endif %}
    </p>
    {% if not current_tournament_id and not current_status %}
    <a href="{{ url_for('tournaments.index') }}" class="btn btn-primary">
        <i class="fas fa-trophy"></i> View Tournaments
    </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}
