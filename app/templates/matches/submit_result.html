{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-trophy"></i> Submit Match Result
                </h4>
                <small class="text-muted">{{ match.teams_display }}</small>
            </div>
            <div class="card-body">
                <!-- Match Info -->
                <div class="alert alert-info">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Tournament:</strong> {{ match.tournament.name }}<br>
                            <strong>Format:</strong> Best of {{ match.best_of }}
                        </div>
                        <div class="col-md-6">
                            <strong>Round:</strong> {{ match.round_number }}<br>
                            <strong>Position:</strong> {{ match.bracket_position or 'N/A' }}
                        </div>
                    </div>
                </div>
                
                <form method="POST">
                    <!-- Overall Match Score -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Overall Match Score</h6>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-4 text-center">
                                    <h6>{{ match.team1.team.name if match.team1 else 'Team 1' }}</h6>
                                    <input type="number" class="form-control form-control-lg text-center" 
                                           name="team1_score" id="team1_score" min="0" max="{{ match.best_of }}" 
                                           value="{{ match.team1_score or 0 }}" required>
                                </div>
                                <div class="col-md-4 text-center">
                                    <h5 class="text-muted">VS</h5>
                                </div>
                                <div class="col-md-4 text-center">
                                    <h6>{{ match.team2.team.name if match.team2 else 'Team 2' }}</h6>
                                    <input type="number" class="form-control form-control-lg text-center" 
                                           name="team2_score" id="team2_score" min="0" max="{{ match.best_of }}" 
                                           value="{{ match.team2_score or 0 }}" required>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    Enter the number of games won by each team (Best of {{ match.best_of }})
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Individual Games (for best-of matches) -->
                    {% if match.best_of > 1 %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Individual Game Results <small class="text-muted">(Optional)</small></h6>
                        </div>
                        <div class="card-body">
                            <div id="games-container">
                                {% for i in range(1, match.best_of + 1) %}
                                <div class="game-row mb-3 p-3 border rounded" id="game-{{ i }}">
                                    <h6>Game {{ i }}</h6>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">Map/Level</label>
                                            <input type="text" class="form-control" name="game_{{ i }}_map" 
                                                   placeholder="e.g., Dust2, Mirage">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">{{ match.team1.team.name if match.team1 else 'Team 1' }} Score</label>
                                            <input type="number" class="form-control" name="game_{{ i }}_team1_score" 
                                                   min="0" placeholder="0">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">{{ match.team2.team.name if match.team2 else 'Team 2' }} Score</label>
                                            <input type="number" class="form-control" name="game_{{ i }}_team2_score" 
                                                   min="0" placeholder="0">
                                        </div>
                                        <div class="col-md-3 d-flex align-items-end">
                                            <button type="button" class="btn btn-outline-secondary btn-sm w-100" 
                                                    onclick="clearGame({{ i }})">
                                                <i class="fas fa-eraser"></i> Clear
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            <div class="alert alert-info">
                                <small>
                                    <i class="fas fa-info-circle"></i>
                                    Individual game results are optional but help provide detailed match history.
                                    The overall match score above is what determines the winner.
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Additional Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Additional Information <small class="text-muted">(Optional)</small></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="stream_url" class="form-label">Stream URL</label>
                                    <input type="url" class="form-control" id="stream_url" name="stream_url" 
                                           value="{{ match.stream_url or '' }}" placeholder="https://twitch.tv/...">
                                </div>
                                <div class="col-md-6">
                                    <label for="duration" class="form-label">Match Duration (minutes)</label>
                                    <input type="number" class="form-control" id="duration" name="duration" 
                                           min="1" placeholder="60">
                                </div>
                            </div>
                            <div class="mt-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="Any additional notes about the match...">{{ match.notes or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('matches.view', id=match.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check"></i> Submit Result
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function clearGame(gameNumber) {
    const gameRow = document.getElementById(`game-${gameNumber}`);
    const inputs = gameRow.querySelectorAll('input');
    inputs.forEach(input => input.value = '');
}

// Auto-calculate winner based on scores
document.addEventListener('DOMContentLoaded', function() {
    const team1ScoreInput = document.getElementById('team1_score');
    const team2ScoreInput = document.getElementById('team2_score');
    
    function updateWinnerDisplay() {
        const team1Score = parseInt(team1ScoreInput.value) || 0;
        const team2Score = parseInt(team2ScoreInput.value) || 0;
        
        // Remove previous winner highlighting
        team1ScoreInput.classList.remove('border-success');
        team2ScoreInput.classList.remove('border-success');
        
        // Highlight winner
        if (team1Score > team2Score && team1Score > 0) {
            team1ScoreInput.classList.add('border-success');
        } else if (team2Score > team1Score && team2Score > 0) {
            team2ScoreInput.classList.add('border-success');
        }
    }
    
    team1ScoreInput.addEventListener('input', updateWinnerDisplay);
    team2ScoreInput.addEventListener('input', updateWinnerDisplay);
    
    // Initial check
    updateWinnerDisplay();
});

// Validate scores before submission
document.querySelector('form').addEventListener('submit', function(e) {
    const team1Score = parseInt(document.getElementById('team1_score').value) || 0;
    const team2Score = parseInt(document.getElementById('team2_score').value) || 0;
    const bestOf = {{ match.best_of }};
    
    if (team1Score === team2Score) {
        e.preventDefault();
        alert('Match cannot end in a tie. Please enter a valid result.');
        return;
    }
    
    if (team1Score > bestOf || team2Score > bestOf) {
        e.preventDefault();
        alert(`Scores cannot exceed ${bestOf} (Best of ${bestOf} format).`);
        return;
    }
    
    const maxPossibleScore = Math.ceil(bestOf / 2);
    if (team1Score > maxPossibleScore || team2Score > maxPossibleScore) {
        e.preventDefault();
        alert(`In a Best of ${bestOf} format, the maximum score is ${maxPossibleScore}.`);
        return;
    }
});
</script>
{% endblock %}
