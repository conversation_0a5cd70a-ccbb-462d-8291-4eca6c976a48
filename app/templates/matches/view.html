{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4" data-match-id="{{ match.id }}">
    <div>
        <h1><i class="fas fa-gamepad"></i> Match Details</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('matches.index') }}">Matches</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('tournaments.view', id=match.tournament.id) }}">{{ match.tournament.name }}</a></li>
                <li class="breadcrumb-item active">{{ match.teams_display }}</li>
            </ol>
        </nav>
    </div>
    <span class="badge badge-lg match-status
        {% if match.status.value == 'scheduled' %}bg-primary
        {% elif match.status.value == 'in_progress' %}bg-warning
        {% elif match.status.value == 'completed' %}bg-success
        {% elif match.status.value == 'cancelled' %}bg-secondary
        {% else %}bg-danger{% endif %}">
        {% if match.status.value == 'in_progress' %}<span class="live-indicator"></span>{% endif %}
        {{ match.status.value.replace('_', ' ').title() }}
    </span>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Match Overview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Match Overview</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <h2 class="mb-3">{{ match.teams_display }}</h2>
                    
                    {% if match.status.value == 'completed' %}
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="row align-items-center">
                                <div class="col-4 text-end">
                                    <h3 class="mb-0 {% if match.winner_id == match.team1_id %}text-success fw-bold{% endif %}">
                                        {{ match.team1_score }}
                                    </h3>
                                </div>
                                <div class="col-4 text-center">
                                    <span class="text-muted fs-4">-</span>
                                </div>
                                <div class="col-4 text-start">
                                    <h3 class="mb-0 {% if match.winner_id == match.team2_id %}text-success fw-bold{% endif %}">
                                        {{ match.team2_score }}
                                    </h3>
                                </div>
                            </div>
                            {% if match.winner %}
                            <div class="mt-3">
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-trophy"></i> {{ match.winner.team.name }} Wins!
                                </span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Tournament</h6>
                        <p><a href="{{ url_for('tournaments.view', id=match.tournament.id) }}">{{ match.tournament.name }}</a></p>
                    </div>
                    <div class="col-md-6">
                        <h6>Format</h6>
                        <p>Best of {{ match.best_of }}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Round</h6>
                        <p>Round {{ match.round_number }}, Match {{ match.match_number }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Bracket Position</h6>
                        <p>{{ match.bracket_position or 'N/A' }}</p>
                    </div>
                </div>
                
                {% if match.scheduled_time %}
                <div class="row">
                    <div class="col-md-6">
                        <h6>Scheduled Time</h6>
                        <p><i class="fas fa-calendar"></i> {{ match.scheduled_time.strftime('%B %d, %Y at %I:%M %p') }}</p>
                    </div>
                    {% if match.started_at %}
                    <div class="col-md-6">
                        <h6>Started</h6>
                        <p><i class="fas fa-play"></i> {{ match.started_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if match.completed_at %}
                <div class="row">
                    <div class="col-md-6">
                        <h6>Completed</h6>
                        <p><i class="fas fa-flag-checkered"></i> {{ match.completed_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if match.stream_url %}
                <div class="row">
                    <div class="col-12">
                        <h6>Stream</h6>
                        <p><a href="{{ match.stream_url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt"></i> Watch Stream
                        </a></p>
                    </div>
                </div>
                {% endif %}
                
                {% if match.notes %}
                <div class="row">
                    <div class="col-12">
                        <h6>Notes</h6>
                        <p>{{ match.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Live Streams -->
        {% set active_streams = match.streams|selectattr('is_active')|list %}
        {% if active_streams %}
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-video text-danger"></i> Live Streams</h5>
                {% if current_user.is_authenticated and (current_user.role.name in ['ADMIN', 'ORGANIZER']) and (current_user.role.name == 'ADMIN' or match.tournament.organizer_id == current_user.id) %}
                <a href="{{ url_for('streams.add_match_stream', match_id=match.id) }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-plus"></i> Add Stream
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% for stream in active_streams %}
                <div class="stream-container mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">
                            <i class="fab fa-{{ stream.platform }}"></i> {{ stream.title }}
                            {% if stream.platform == 'twitch' %}
                            <span class="badge bg-purple">Twitch</span>
                            {% elif stream.platform == 'youtube' %}
                            <span class="badge bg-danger">YouTube</span>
                            {% endif %}
                        </h6>
                        {% if current_user.is_authenticated and (current_user.role.name in ['ADMIN', 'ORGANIZER']) and (current_user.role.name == 'ADMIN' or match.tournament.organizer_id == current_user.id) %}
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <form method="POST" action="{{ url_for('streams.deactivate_stream', stream_id=stream.id) }}" class="d-inline">
                                        <button type="submit" class="dropdown-item text-warning">
                                            <i class="fas fa-pause"></i> Deactivate
                                        </button>
                                    </form>
                                </li>
                                <li>
                                    <form method="POST" action="{{ url_for('streams.delete_stream', stream_id=stream.id) }}" class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this stream?')">
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% if stream.description %}
                    <p class="text-muted small mb-2">{{ stream.description }}</p>
                    {% endif %}
                    <div class="ratio ratio-16x9">
                        {% if stream.platform == 'twitch' %}
                        <iframe
                            src="{{ stream.embed_url }}"
                            allowfullscreen="true"
                            scrolling="no"
                            frameborder="0">
                        </iframe>
                        {% elif stream.platform == 'youtube' %}
                        <iframe
                            src="{{ stream.embed_url }}"
                            title="{{ stream.title }}"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen>
                        </iframe>
                        {% else %}
                        <iframe
                            src="{{ stream.embed_url }}"
                            frameborder="0"
                            allowfullscreen>
                        </iframe>
                        {% endif %}
                    </div>
                    <div class="mt-2">
                        <a href="{{ stream.stream_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> Open in {{ stream.platform.title() }}
                        </a>
                    </div>
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
        {% elif current_user.is_authenticated and (current_user.role.name in ['ADMIN', 'ORGANIZER']) and (current_user.role.name == 'ADMIN' or match.tournament.organizer_id == current_user.id) %}
        <div class="card mb-4">
            <div class="card-body text-center">
                <i class="fas fa-video fa-3x text-muted mb-3"></i>
                <h6>No Live Streams</h6>
                <p class="text-muted small">Add a live stream for this match.</p>
                <a href="{{ url_for('streams.add_match_stream', match_id=match.id) }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Add Stream
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Individual Games (for best-of matches) -->
        {% if games %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> Game Results</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Game</th>
                                <th>Map</th>
                                <th>{{ match.team1.team.name if match.team1 else 'Team 1' }}</th>
                                <th>{{ match.team2.team.name if match.team2 else 'Team 2' }}</th>
                                <th>Winner</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for game in games %}
                            <tr>
                                <td>Game {{ game.game_number }}</td>
                                <td>{{ game.map_played or 'N/A' }}</td>
                                <td class="{% if game.winner_id == match.team1_id %}fw-bold text-success{% endif %}">
                                    {{ game.team1_score }}
                                </td>
                                <td class="{% if game.winner_id == match.team2_id %}fw-bold text-success{% endif %}">
                                    {{ game.team2_score }}
                                </td>
                                <td>
                                    {% if game.winner_id == match.team1_id %}
                                        {{ match.team1.team.name }}
                                    {% elif game.winner_id == match.team2_id %}
                                        {{ match.team2.team.name }}
                                    {% else %}
                                        Draw
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <!-- Actions -->
        {% if current_user.is_authenticated %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cogs"></i> Actions</h6>
            </div>
            <div class="card-body">
                {% if match.status.value == 'scheduled' and (current_user.role.value == 'admin' or current_user.id == match.tournament.organizer_id) %}
                <form method="POST" action="{{ url_for('matches.start_match', id=match.id) }}" class="mb-2">
                    <button type="submit" class="btn btn-success btn-sm w-100">
                        <i class="fas fa-play"></i> Start Match
                    </button>
                </form>
                {% endif %}
                
                {% if match.status.value in ['scheduled', 'in_progress'] and (current_user.role.value in ['admin', 'organizer'] or current_user.id == match.tournament.organizer_id or (match.team1 and match.team1.team.captain_id == current_user.id) or (match.team2 and match.team2.team.captain_id == current_user.id)) %}
                <a href="{{ url_for('matches.submit_result', id=match.id) }}" class="btn btn-primary btn-sm w-100 mb-2">
                    <i class="fas fa-trophy"></i> Submit Result
                </a>
                {% endif %}
                
                {% if match.status.value != 'completed' and (current_user.role.value == 'admin' or current_user.id == match.tournament.organizer_id) %}
                <button type="button" class="btn btn-warning btn-sm w-100 mb-2" data-bs-toggle="modal" data-bs-target="#rescheduleModal">
                    <i class="fas fa-clock"></i> Reschedule
                </button>
                
                <form method="POST" action="{{ url_for('matches.cancel_match', id=match.id) }}" class="mb-2" 
                      onsubmit="return confirm('Are you sure you want to cancel this match?')">
                    <button type="submit" class="btn btn-danger btn-sm w-100">
                        <i class="fas fa-times"></i> Cancel Match
                    </button>
                </form>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Teams -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-users"></i> Teams</h6>
            </div>
            <div class="card-body">
                {% if match.team1 %}
                <div class="mb-3">
                    <h6>{{ match.team1.team.name }}</h6>
                    <small class="text-muted">Seed: {{ match.team1.seed or 'N/A' }}</small>
                </div>
                {% endif %}
                
                {% if match.team2 %}
                <div class="mb-3">
                    <h6>{{ match.team2.team.name }}</h6>
                    <small class="text-muted">Seed: {{ match.team2.seed or 'N/A' }}</small>
                </div>
                {% endif %}
                
                {% if not match.team1 or not match.team2 %}
                <p class="text-muted">Teams will be determined from previous matches.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Reschedule Modal -->
{% if current_user.is_authenticated and (current_user.role.value == 'admin' or current_user.id == match.tournament.organizer_id) %}
<div class="modal fade" id="rescheduleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reschedule Match</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('matches.reschedule_match', id=match.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_time" class="form-label">New Date & Time</label>
                        <input type="datetime-local" class="form-control" id="new_time" name="new_time" 
                               value="{{ match.scheduled_time.strftime('%Y-%m-%dT%H:%M') if match.scheduled_time else '' }}" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Reschedule</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
