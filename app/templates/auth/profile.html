{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-user-circle fa-5x text-muted mb-3"></i>
                <h4>{{ user.first_name }} {{ user.last_name }}</h4>
                <p class="text-muted">@{{ user.username }}</p>
                <span class="badge bg-{{ 'primary' if user.role.value == 'player' else 'success' if user.role.value == 'organizer' else 'danger' }} mb-3">
                    {{ user.role.value.title() }}
                </span>
                {% if user.bio %}
                    <p class="mt-3">{{ user.bio }}</p>
                {% endif %}
                
                <div class="mt-3">
                    <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit"></i> Edit Profile
                    </a>
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-key"></i> Change Password
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-address-card"></i> Contact Information</h6>
            </div>
            <div class="card-body">
                <p><strong>Email:</strong> {{ user.email }}</p>
                {% if user.discord_username %}
                    <p><strong>Discord:</strong> {{ user.discord_username }}</p>
                {% endif %}
                {% if user.twitch_username %}
                    <p><strong>Twitch:</strong> {{ user.twitch_username }}</p>
                {% endif %}
                <p><strong>Member since:</strong> {{ user.created_at.strftime('%B %Y') }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        {% if user.role.value == 'player' and user.player_profile %}
            <!-- Player Profile -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-gamepad"></i> Player Profile</h5>
                    <a href="{{ url_for('auth.player_profile') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>In-Game Name:</strong> {{ user.player_profile.in_game_name }}</p>
                            <p><strong>Skill Level:</strong> {{ user.player_profile.skill_level or 'Not specified' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Preferred Games:</strong> {{ user.player_profile.preferred_games or 'Not specified' }}</p>
                            <p><strong>Matches Played:</strong> {{ user.player_profile.matches_played }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-success">{{ user.player_profile.wins }}</h4>
                                <small class="text-muted">Wins</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-danger">{{ user.player_profile.losses }}</h4>
                                <small class="text-muted">Losses</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-primary">{{ "%.1f"|format(user.player_profile.win_rate) }}%</h4>
                                <small class="text-muted">Win Rate</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Team Memberships -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-users"></i> Team Memberships</h5>
                </div>
                <div class="card-body">
                    {% if user.player_profile.team %}
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6>{{ user.player_profile.team.name }}</h6>
                                <p class="text-muted mb-0">{{ user.player_profile.team.description }}</p>
                            </div>
                            <a href="{{ url_for('teams.view', id=user.player_profile.team.id) }}" class="btn btn-outline-primary btn-sm">
                                View Team
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted">Not currently on any team.</p>
                        <a href="{{ url_for('teams.index') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-search"></i> Find a Team
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
        
        <!-- Recent Activity -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history"></i> Recent Activity</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6>Account Created</h6>
                            <p class="text-muted">{{ user.created_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                        </div>
                    </div>
                    {% if user.last_seen %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6>Last Active</h6>
                                <p class="text-muted">{{ user.last_seen.strftime('%B %d, %Y at %I:%M %p') }}</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}
</style>
{% endblock %}
