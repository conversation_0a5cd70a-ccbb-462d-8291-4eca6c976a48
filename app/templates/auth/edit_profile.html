{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-user-edit"></i> Edit Profile
                </h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.first_name.label(class="form-label") }}
                                {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                                {% if form.first_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.first_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.last_name.label(class="form-label") }}
                                {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                                {% if form.last_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.last_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.bio.label(class="form-label") }}
                        {{ form.bio(class="form-control" + (" is-invalid" if form.bio.errors else ""), rows="4") }}
                        {% if form.bio.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bio.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Tell others about yourself (optional)</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.discord_username.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-discord"></i></span>
                                    {{ form.discord_username(class="form-control" + (" is-invalid" if form.discord_username.errors else "")) }}
                                </div>
                                {% if form.discord_username.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.discord_username.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.twitch_username.label(class="form-label") }}
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-twitch"></i></span>
                                    {{ form.twitch_username(class="form-control" + (" is-invalid" if form.twitch_username.errors else "")) }}
                                </div>
                                {% if form.twitch_username.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.twitch_username.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Profile
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
