{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-gamepad"></i> Player Profile Settings
                </h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.in_game_name.label(class="form-label") }}
                        {{ form.in_game_name(class="form-control" + (" is-invalid" if form.in_game_name.errors else "")) }}
                        {% if form.in_game_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.in_game_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Your display name in tournaments and matches</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.preferred_games.label(class="form-label") }}
                        {{ form.preferred_games(class="form-control" + (" is-invalid" if form.preferred_games.errors else "")) }}
                        {% if form.preferred_games.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.preferred_games.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ form.preferred_games.description }}</div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.skill_level.label(class="form-label") }}
                        {{ form.skill_level(class="form-select" + (" is-invalid" if form.skill_level.errors else "")) }}
                        {% if form.skill_level.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.skill_level.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Help teams find players of appropriate skill level</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Note:</strong> Your player statistics (wins, losses, matches played) are automatically updated based on your tournament participation.
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Profile
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
