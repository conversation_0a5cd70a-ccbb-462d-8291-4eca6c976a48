# API routes - to be implemented
from flask import jsonify
from app.api import bp


@bp.route('/tournaments')
def tournaments():
    """API endpoint for tournaments."""
    return jsonify({"message": "Tournaments API - to be implemented"})


@bp.route('/teams')
def teams():
    """API endpoint for teams."""
    return jsonify({"message": "Teams API - to be implemented"})


@bp.route('/matches')
def matches():
    """API endpoint for matches."""
    return jsonify({"message": "Matches API - to be implemented"})
