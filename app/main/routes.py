from flask import render_template, request, current_app
from app.main import bp
from app.models import Tournament, Match, Team, Player, TournamentStatus
from app import db


@bp.route('/')
@bp.route('/index')
def index():
    """Home page with tournament listings and statistics."""
    # Get featured tournaments (open tournaments)
    featured_tournaments = Tournament.query.filter_by(status=TournamentStatus.REGISTRATION_OPEN)\
        .order_by(Tournament.created_at.desc())\
        .limit(4).all()

    # Get statistics
    tournament_count = Tournament.query.count()
    team_count = Team.query.count()
    player_count = Player.query.count()
    match_count = Match.query.count()

    return render_template('main/index.html',
                         title='Home',
                         featured_tournaments=featured_tournaments,
                         tournament_count=tournament_count,
                         team_count=team_count,
                         player_count=player_count,
                         match_count=match_count)


@bp.route('/about')
def about():
    """About page."""
    return render_template('main/about.html')


@bp.route('/contact')
def contact():
    """Contact page."""
    return render_template('main/contact.html')
