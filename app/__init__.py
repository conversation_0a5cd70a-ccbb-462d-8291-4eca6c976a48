from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import <PERSON>ginManager
from flask_cors import CORS
from flask_socketio import Socket<PERSON>
from config import config

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
socketio = SocketIO()


def create_app(config_name='default'):
    """Application factory pattern."""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions with app
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)
    CORS(app)
    socketio.init_app(app, cors_allowed_origins="*")

    # Initialize Discord service
    from app.services.discord_service import discord_service
    discord_service.init_app(app)

    # Initialize Stream service
    from app.services.stream_service import stream_service
    stream_service.init_app(app)
    
    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    # Register blueprints
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.tournaments import bp as tournaments_bp
    app.register_blueprint(tournaments_bp, url_prefix='/tournaments')
    
    from app.teams import bp as teams_bp
    app.register_blueprint(teams_bp, url_prefix='/teams')
    
    from app.matches import bp as matches_bp
    app.register_blueprint(matches_bp, url_prefix='/matches')

    from app.leaderboards import bp as leaderboards_bp
    app.register_blueprint(leaderboards_bp, url_prefix='/leaderboards')

    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')

    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    from app.streams import bp as streams_bp
    app.register_blueprint(streams_bp, url_prefix='/streams')

    # Import models to ensure they are registered with SQLAlchemy
    from app import models

    # Import WebSocket events
    from app import websocket_events

    return app


@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login."""
    from app.models import User
    return User.query.get(int(user_id))
