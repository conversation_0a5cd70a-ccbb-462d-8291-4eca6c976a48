# Team management routes - to be implemented
from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from sqlalchemy import or_, desc
from app.teams import bp
from app.models import Team, Player, UserRole
from app.services.team_service import TeamService
from app import db


@bp.route('/')
def index():
    """List all teams with search and filtering."""
    # Get search parameters
    search = request.args.get('search', '').strip()
    sort_by = request.args.get('sort', 'name')  # name, created_at, players_count
    order = request.args.get('order', 'asc')  # asc, desc
    min_players = request.args.get('min_players', type=int)
    max_players = request.args.get('max_players', type=int)
    has_captain = request.args.get('has_captain')  # 'yes', 'no', or None
    page = request.args.get('page', 1, type=int)
    per_page = 12  # Teams per page

    # Base query for active teams
    query = Team.query.filter_by(is_active=True)

    # Apply search filter
    if search:
        query = query.filter(
            or_(
                Team.name.ilike(f'%{search}%'),
                Team.tag.ilike(f'%{search}%'),
                Team.description.ilike(f'%{search}%')
            )
        )

    # Apply captain filter
    if has_captain == 'yes':
        query = query.filter(Team.captain_id.isnot(None))
    elif has_captain == 'no':
        query = query.filter(Team.captain_id.is_(None))

    # For player count filtering, we need to join with Player table
    if min_players is not None or max_players is not None or sort_by == 'players_count':
        from sqlalchemy import func
        # Subquery to count active players per team
        player_counts = db.session.query(
            Player.team_id,
            func.count(Player.id).label('player_count')
        ).filter(Player.is_active == True).group_by(Player.team_id).subquery()

        # Join with the player counts
        query = query.outerjoin(player_counts, Team.id == player_counts.c.team_id)

        # Apply player count filters
        if min_players is not None:
            query = query.filter(
                or_(
                    player_counts.c.player_count >= min_players,
                    player_counts.c.player_count.is_(None)  # Teams with no players
                )
            )
        if max_players is not None:
            query = query.filter(
                or_(
                    player_counts.c.player_count <= max_players,
                    player_counts.c.player_count.is_(None)  # Teams with no players
                )
            )

        # Apply sorting with player count
        if sort_by == 'players_count':
            if order == 'desc':
                query = query.order_by(desc(func.coalesce(player_counts.c.player_count, 0)))
            else:
                query = query.order_by(func.coalesce(player_counts.c.player_count, 0))
        elif sort_by == 'created_at':
            if order == 'desc':
                query = query.order_by(desc(Team.created_at))
            else:
                query = query.order_by(Team.created_at)
        else:  # sort by name (default)
            if order == 'desc':
                query = query.order_by(desc(Team.name))
            else:
                query = query.order_by(Team.name)
    else:
        # Apply sorting without player count
        if sort_by == 'created_at':
            if order == 'desc':
                query = query.order_by(desc(Team.created_at))
            else:
                query = query.order_by(Team.created_at)
        else:  # sort by name (default)
            if order == 'desc':
                query = query.order_by(desc(Team.name))
            else:
                query = query.order_by(Team.name)

    # Paginate results
    teams = query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    return render_template('teams/index.html',
                         title='Teams',
                         teams=teams,
                         search=search,
                         sort_by=sort_by,
                         order=order,
                         min_players=min_players,
                         max_players=max_players,
                         has_captain=has_captain)


@bp.route('/create')
def create():
    """Create new team."""
    return "Create team - to be implemented"


@bp.route('/<int:id>')
def view(id):
    """View team details."""
    team = Team.query.get_or_404(id)

    if not team.is_active:
        flash('This team is no longer active.', 'error')
        return redirect(url_for('teams.index'))

    # Get comprehensive team statistics using the service
    team_stats = TeamService.get_team_statistics(team)

    # Get team members with their statistics
    members = TeamService.get_team_members_with_stats(team)

    # Get tournament registrations
    tournament_registrations = team.tournament_registrations.filter_by(is_active=True).all()

    return render_template('teams/view.html',
                         title=f'{team.name} - Team Details',
                         team=team,
                         members=members,
                         tournament_registrations=tournament_registrations,
                         team_stats=team_stats)


@bp.route('/my-teams')
@login_required
def my_teams():
    """View teams where current user is captain or member."""
    if current_user.role != UserRole.PLAYER:
        flash('Only players can view team memberships.', 'error')
        return redirect(url_for('teams.index'))

    # Check if user has a player profile
    if not current_user.player_profile:
        flash('You need to complete your player profile first.', 'info')
        return redirect(url_for('auth.profile'))

    player = current_user.player_profile

    # Get teams where user is captain
    captain_teams = Team.query.filter_by(
        captain_id=player.id,
        is_active=True
    ).order_by(Team.created_at.desc()).all()

    # Get team where user is a member (if any)
    member_team = player.team if player.team and player.team.is_active else None

    return render_template('teams/my_teams.html',
                         title='My Teams',
                         captain_teams=captain_teams,
                         member_team=member_team)
