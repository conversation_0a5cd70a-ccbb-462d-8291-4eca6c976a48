"""Match management routes."""
from flask import render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from app.matches import bp
from app.models import Match, Tournament, TournamentTeam, MatchStatus, UserRole, Game
from app.services.match_scheduler import MatchScheduler
from app.services.discord_service import discord_service
from app import db


@bp.route('/')
def index():
    """List all matches."""
    page = request.args.get('page', 1, type=int)
    tournament_id = request.args.get('tournament_id', type=int)
    status = request.args.get('status')

    query = Match.query

    # Filter by tournament if specified
    if tournament_id:
        query = query.filter_by(tournament_id=tournament_id)

    # Filter by status if specified
    if status and status in [s.value for s in MatchStatus]:
        query = query.filter_by(status=MatchStatus(status))

    # Order by scheduled time, then by round and match number
    query = query.order_by(
        Match.scheduled_time.asc().nullslast(),
        Match.round_number.asc(),
        Match.match_number.asc()
    )

    matches = query.paginate(
        page=page, per_page=20, error_out=False
    )

    # Get tournaments for filter dropdown
    tournaments = Tournament.query.filter_by(is_active=True).order_by(Tournament.name).all()

    return render_template('matches/index.html',
                         matches=matches,
                         tournaments=tournaments,
                         current_tournament_id=tournament_id,
                         current_status=status)


@bp.route('/<int:id>')
def view(id):
    """View match details."""
    match = Match.query.get_or_404(id)

    # Get match games if any
    games = Game.query.filter_by(match_id=match.id).order_by(Game.game_number).all()

    return render_template('matches/view.html', match=match, games=games)


@bp.route('/<int:id>/result', methods=['GET', 'POST'])
@login_required
def submit_result(id):
    """Submit match result."""
    match = Match.query.get_or_404(id)

    # Check permissions
    if not _can_submit_result(match):
        flash('You do not have permission to submit results for this match.', 'error')
        return redirect(url_for('matches.view', id=id))

    if request.method == 'POST':
        try:
            # Get form data
            team1_score = int(request.form.get('team1_score', 0))
            team2_score = int(request.form.get('team2_score', 0))

            # Validate scores
            if team1_score < 0 or team2_score < 0:
                flash('Scores cannot be negative.', 'error')
                return render_template('matches/submit_result.html', match=match)

            if team1_score == team2_score:
                flash('Match cannot end in a tie. Please enter a valid result.', 'error')
                return render_template('matches/submit_result.html', match=match)

            # Determine winner
            if team1_score > team2_score:
                winner_id = match.team1_id
            else:
                winner_id = match.team2_id

            # Update match
            match.team1_score = team1_score
            match.team2_score = team2_score
            match.winner_id = winner_id
            match.status = MatchStatus.COMPLETED
            match.completed_at = datetime.utcnow()

            # Handle individual games if best-of format
            if match.best_of > 1:
                _process_individual_games(match, request.form)

            db.session.commit()

            # Send Discord notification
            discord_service.notify_match_result(match)

            # Broadcast WebSocket update
            from app.websocket_events import broadcast_match_result
            broadcast_match_result(match)

            # Advance bracket if needed
            scheduler = MatchScheduler(match.tournament)
            try:
                new_matches = scheduler.advance_bracket(match)
                if new_matches:
                    flash(f'Result submitted successfully! {len(new_matches)} new matches generated.', 'success')
                else:
                    flash('Result submitted successfully!', 'success')

                # Check if tournament is complete
                if scheduler.check_tournament_completion():
                    flash('Tournament has been completed!', 'info')

                # Update tournament placements
                from app.services.leaderboard_service import LeaderboardService
                leaderboard_service = LeaderboardService(match.tournament)
                leaderboard_service.update_tournament_placements()

            except Exception as e:
                flash(f'Result submitted, but error advancing bracket: {str(e)}', 'warning')

            return redirect(url_for('matches.view', id=id))

        except ValueError:
            flash('Invalid score values. Please enter valid numbers.', 'error')
        except Exception as e:
            db.session.rollback()
            flash(f'Error submitting result: {str(e)}', 'error')

    return render_template('matches/submit_result.html', match=match)


@bp.route('/<int:id>/start', methods=['POST'])
@login_required
def start_match(id):
    """Start a match."""
    match = Match.query.get_or_404(id)

    # Check permissions
    if not _can_manage_match(match):
        flash('You do not have permission to start this match.', 'error')
        return redirect(url_for('matches.view', id=id))

    if match.status != MatchStatus.SCHEDULED:
        flash('Only scheduled matches can be started.', 'error')
        return redirect(url_for('matches.view', id=id))

    try:
        match.status = MatchStatus.IN_PROGRESS
        match.started_at = datetime.utcnow()
        db.session.commit()
        flash('Match started successfully!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error starting match: {str(e)}', 'error')

    return redirect(url_for('matches.view', id=id))


@bp.route('/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_match(id):
    """Cancel a match."""
    match = Match.query.get_or_404(id)

    # Check permissions
    if not _can_manage_match(match):
        flash('You do not have permission to cancel this match.', 'error')
        return redirect(url_for('matches.view', id=id))

    if match.status == MatchStatus.COMPLETED:
        flash('Cannot cancel a completed match.', 'error')
        return redirect(url_for('matches.view', id=id))

    try:
        match.status = MatchStatus.CANCELLED
        db.session.commit()
        flash('Match cancelled successfully!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error cancelling match: {str(e)}', 'error')

    return redirect(url_for('matches.view', id=id))


@bp.route('/<int:id>/reschedule', methods=['POST'])
@login_required
def reschedule_match(id):
    """Reschedule a match."""
    match = Match.query.get_or_404(id)

    # Check permissions
    if not _can_manage_match(match):
        flash('You do not have permission to reschedule this match.', 'error')
        return redirect(url_for('matches.view', id=id))

    if match.status not in [MatchStatus.SCHEDULED, MatchStatus.IN_PROGRESS]:
        flash('Only scheduled or in-progress matches can be rescheduled.', 'error')
        return redirect(url_for('matches.view', id=id))

    try:
        new_time_str = request.form.get('new_time')
        if not new_time_str:
            flash('Please provide a new time.', 'error')
            return redirect(url_for('matches.view', id=id))

        new_time = datetime.fromisoformat(new_time_str.replace('T', ' '))

        scheduler = MatchScheduler(match.tournament)
        if scheduler.reschedule_match(match, new_time):
            flash('Match rescheduled successfully!', 'success')
        else:
            flash('Error rescheduling match.', 'error')

    except ValueError:
        flash('Invalid date/time format.', 'error')
    except Exception as e:
        flash(f'Error rescheduling match: {str(e)}', 'error')

    return redirect(url_for('matches.view', id=id))


def _can_submit_result(match: Match) -> bool:
    """Check if current user can submit result for match."""
    if current_user.role == UserRole.ADMIN:
        return True

    # Tournament organizer can submit results
    if current_user.id == match.tournament.organizer_id:
        return True

    # Team captains can submit results (in a real system, this might require both teams to confirm)
    if match.team1 and match.team1.team.captain_id == current_user.id:
        return True
    if match.team2 and match.team2.team.captain_id == current_user.id:
        return True

    return False


def _can_manage_match(match: Match) -> bool:
    """Check if current user can manage (start/cancel/reschedule) match."""
    if current_user.role == UserRole.ADMIN:
        return True

    # Tournament organizer can manage matches
    if current_user.id == match.tournament.organizer_id:
        return True

    return False


def _process_individual_games(match: Match, form_data: dict) -> None:
    """Process individual game results for best-of matches."""
    # Clear existing games
    Game.query.filter_by(match_id=match.id).delete()

    # Process each game
    for i in range(1, match.best_of + 1):
        team1_game_score = form_data.get(f'game_{i}_team1_score')
        team2_game_score = form_data.get(f'game_{i}_team2_score')
        map_played = form_data.get(f'game_{i}_map')

        if team1_game_score is not None and team2_game_score is not None:
            try:
                team1_score = int(team1_game_score)
                team2_score = int(team2_game_score)

                # Determine winner
                winner_id = None
                if team1_score > team2_score:
                    winner_id = match.team1_id
                elif team2_score > team1_score:
                    winner_id = match.team2_id

                game = Game(
                    match_id=match.id,
                    game_number=i,
                    team1_score=team1_score,
                    team2_score=team2_score,
                    winner_id=winner_id,
                    map_played=map_played,
                    completed_at=datetime.utcnow()
                )
                db.session.add(game)

            except ValueError:
                continue  # Skip invalid game data
