from datetime import datetime, timezone
from decimal import Decimal
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import Numeric
from app import db
import enum


class UserRole(enum.Enum):
    """User roles in the system."""
    PLAYER = "player"
    ORGANIZER = "organizer"
    ADMIN = "admin"


class TournamentStatus(enum.Enum):
    """Tournament status options."""
    DRAFT = "draft"
    REGISTRATION_OPEN = "registration_open"
    REGISTRATION_CLOSED = "registration_closed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TournamentFormat(enum.Enum):
    """Tournament bracket formats."""
    SINGLE_ELIMINATION = "single_elimination"
    DOUBLE_ELIMINATION = "double_elimination"
    ROUND_ROBIN = "round_robin"
    SWISS = "swiss"


class MatchStatus(enum.Enum):
    """Match status options."""
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    DISPUTED = "disputed"


class User(UserMixin, db.Model):
    """User model for authentication and profiles."""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    role = db.Column(db.Enum(UserRole), default=UserRole.PLAYER, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Profile information
    avatar_url = db.Column(db.String(255))
    bio = db.Column(db.Text)
    discord_username = db.Column(db.String(100))
    twitch_username = db.Column(db.String(100))
    
    # Relationships
    organized_tournaments = db.relationship('Tournament', backref='organizer', lazy='dynamic')
    player_profile = db.relationship('Player', backref='user', uselist=False)
    
    def set_password(self, password):
        """Set password hash."""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash."""
        return check_password_hash(self.password_hash, password)
    
    @property
    def full_name(self):
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}"
    
    def __repr__(self):
        return f'<User {self.username}>'


class Tournament(db.Model):
    """Tournament model."""
    __tablename__ = 'tournaments'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    game = db.Column(db.String(100), nullable=False)  # e.g., "Valorant", "Dota 2", "BGMI"
    format = db.Column(db.Enum(TournamentFormat), default=TournamentFormat.SINGLE_ELIMINATION)
    status = db.Column(db.Enum(TournamentStatus), default=TournamentStatus.DRAFT)
    
    # Tournament settings
    max_teams = db.Column(db.Integer, default=16, nullable=False)
    team_size = db.Column(db.Integer, default=5, nullable=False)
    entry_fee = db.Column(Numeric(10, 2), default=0.00)
    prize_pool = db.Column(Numeric(10, 2), default=0.00)
    
    # Dates
    registration_start = db.Column(db.DateTime, nullable=False)
    registration_end = db.Column(db.DateTime, nullable=False)
    tournament_start = db.Column(db.DateTime, nullable=False)
    tournament_end = db.Column(db.DateTime)
    
    # Metadata
    organizer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Tournament rules and settings
    rules = db.Column(db.Text)
    stream_url = db.Column(db.String(255))
    discord_server = db.Column(db.String(255))
    
    # Relationships
    teams = db.relationship('TournamentTeam', backref='tournament', lazy='dynamic', cascade='all, delete-orphan')
    matches = db.relationship('Match', backref='tournament', lazy='dynamic', cascade='all, delete-orphan')
    
    @property
    def registered_teams_count(self):
        """Get count of registered teams."""
        return self.teams.filter_by(is_active=True).count()
    
    @property
    def is_registration_open(self):
        """Check if registration is currently open."""
        now = datetime.utcnow()
        return (self.status == TournamentStatus.REGISTRATION_OPEN and 
                self.registration_start <= now <= self.registration_end)
    
    def __repr__(self):
        return f'<Tournament {self.name}>'


class Team(db.Model):
    """Team model."""
    __tablename__ = 'teams'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    tag = db.Column(db.String(10), nullable=False)  # Team abbreviation/tag
    description = db.Column(db.Text)
    logo_url = db.Column(db.String(255))

    # Team captain (must be a player) - will be set after player creation
    captain_id = db.Column(db.Integer, db.ForeignKey('players.id'))

    # Metadata
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    players = db.relationship('Player', backref='team', lazy='dynamic', foreign_keys='Player.team_id')
    captain = db.relationship('Player', foreign_keys=[captain_id], post_update=True)
    tournament_registrations = db.relationship('TournamentTeam', backref='team', lazy='dynamic')

    @property
    def active_players_count(self):
        """Get count of active players."""
        return self.players.filter_by(is_active=True).count()

    def __repr__(self):
        return f'<Team {self.name}>'


class Player(db.Model):
    """Player model - extends User with gaming-specific information."""
    __tablename__ = 'players'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.id'))

    # Gaming information
    in_game_name = db.Column(db.String(100), nullable=False)
    preferred_games = db.Column(db.String(255))  # Comma-separated list
    skill_level = db.Column(db.String(50))  # e.g., "Beginner", "Intermediate", "Advanced", "Professional"

    # Statistics
    matches_played = db.Column(db.Integer, default=0)
    matches_won = db.Column(db.Integer, default=0)
    tournaments_participated = db.Column(db.Integer, default=0)
    tournaments_won = db.Column(db.Integer, default=0)

    # Metadata
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    joined_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    @property
    def win_rate(self):
        """Calculate player's win rate."""
        if self.matches_played == 0:
            return 0.0
        return (self.matches_won / self.matches_played) * 100

    def __repr__(self):
        return f'<Player {self.in_game_name}>'


class TournamentTeam(db.Model):
    """Association table for tournament team registrations."""
    __tablename__ = 'tournament_teams'

    id = db.Column(db.Integer, primary_key=True)
    tournament_id = db.Column(db.Integer, db.ForeignKey('tournaments.id'), nullable=False)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.id'), nullable=False)

    # Registration details
    registered_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    seed = db.Column(db.Integer)  # Tournament seeding

    # Tournament performance
    placement = db.Column(db.Integer)  # Final placement in tournament
    prize_won = db.Column(Numeric(10, 2), default=0.00)

    # Unique constraint to prevent duplicate registrations
    __table_args__ = (db.UniqueConstraint('tournament_id', 'team_id', name='unique_tournament_team'),)

    def __repr__(self):
        return f'<TournamentTeam {self.team.name} in {self.tournament.name}>'


class Match(db.Model):
    """Match model for tournament matches."""
    __tablename__ = 'matches'

    id = db.Column(db.Integer, primary_key=True)
    tournament_id = db.Column(db.Integer, db.ForeignKey('tournaments.id'), nullable=False)

    # Match details
    round_number = db.Column(db.Integer, nullable=False)
    match_number = db.Column(db.Integer, nullable=False)
    bracket_position = db.Column(db.String(50))  # e.g., "WB-R1-M1", "LB-R2-M3"

    # Teams
    team1_id = db.Column(db.Integer, db.ForeignKey('tournament_teams.id'))
    team2_id = db.Column(db.Integer, db.ForeignKey('tournament_teams.id'))
    winner_id = db.Column(db.Integer, db.ForeignKey('tournament_teams.id'))

    # Match settings
    best_of = db.Column(db.Integer, default=1, nullable=False)  # Best of X games
    status = db.Column(db.Enum(MatchStatus), default=MatchStatus.SCHEDULED)

    # Scheduling
    scheduled_time = db.Column(db.DateTime)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)

    # Results
    team1_score = db.Column(db.Integer, default=0)
    team2_score = db.Column(db.Integer, default=0)

    # Additional information
    stream_url = db.Column(db.String(255))
    notes = db.Column(db.Text)

    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    team1 = db.relationship('TournamentTeam', foreign_keys=[team1_id], backref='matches_as_team1')
    team2 = db.relationship('TournamentTeam', foreign_keys=[team2_id], backref='matches_as_team2')
    winner = db.relationship('TournamentTeam', foreign_keys=[winner_id], backref='matches_won')
    games = db.relationship('Game', backref='match', lazy='dynamic', cascade='all, delete-orphan')

    @property
    def is_completed(self):
        """Check if match is completed."""
        return self.status == MatchStatus.COMPLETED

    @property
    def teams_display(self):
        """Get display string for teams."""
        team1_name = self.team1.team.name if self.team1 else "TBD"
        team2_name = self.team2.team.name if self.team2 else "TBD"
        return f"{team1_name} vs {team2_name}"

    def __repr__(self):
        return f'<Match {self.teams_display}>'


class Game(db.Model):
    """Individual game within a match (for best-of-X matches)."""
    __tablename__ = 'games'

    id = db.Column(db.Integer, primary_key=True)
    match_id = db.Column(db.Integer, db.ForeignKey('matches.id'), nullable=False)
    game_number = db.Column(db.Integer, nullable=False)

    # Game results
    team1_score = db.Column(db.Integer)
    team2_score = db.Column(db.Integer)
    winner_id = db.Column(db.Integer, db.ForeignKey('tournament_teams.id'))

    # Game details
    map_played = db.Column(db.String(100))  # Map/level name
    duration = db.Column(db.Integer)  # Duration in seconds

    # Timestamps
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)

    # Additional data (JSON for game-specific stats)
    game_data = db.Column(db.JSON)

    # Relationships
    winner = db.relationship('TournamentTeam', backref='games_won')

    def __repr__(self):
        return f'<Game {self.game_number} of Match {self.match_id}>'


class Stream(db.Model):
    """Stream information for tournaments and matches."""
    __tablename__ = 'streams'

    id = db.Column(db.Integer, primary_key=True)
    tournament_id = db.Column(db.Integer, db.ForeignKey('tournaments.id'), nullable=False)
    match_id = db.Column(db.Integer, db.ForeignKey('matches.id'), nullable=True)  # Optional for tournament-wide streams
    platform = db.Column(db.String(20), nullable=False)  # 'twitch', 'youtube', etc.
    stream_id = db.Column(db.String(100), nullable=False)  # Channel name or video ID
    stream_url = db.Column(db.String(500), nullable=False)  # Original URL
    embed_url = db.Column(db.String(500), nullable=False)  # Embeddable URL
    title = db.Column(db.String(200), nullable=True)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    tournament = db.relationship('Tournament', backref='streams')
    match = db.relationship('Match', backref='streams')

    def __repr__(self):
        return f'<Stream {self.platform}:{self.stream_id}>'
