from flask_wtf import FlaskForm
from wtforms import <PERSON>F<PERSON>, PasswordField, BooleanField, SubmitField, SelectField, TextAreaField
from wtforms.validators import <PERSON>Required, Email, EqualTo, Length, ValidationError
from app.models import User, UserRole


class LoginForm(FlaskForm):
    """User login form."""
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Sign In')


class RegistrationForm(FlaskForm):
    """User registration form."""
    username = StringField('Username', validators=[
        DataRequired(), 
        Length(min=4, max=20, message='Username must be between 4 and 20 characters')
    ])
    email = StringField('Email', validators=[DataRequired(), Email()])
    first_name = <PERSON><PERSON>ield('First Name', validators=[
        DataRequired(),
        Length(min=2, max=50, message='First name must be between 2 and 50 characters')
    ])
    last_name = StringField('Last Name', validators=[
        DataRequired(),
        Length(min=2, max=50, message='Last name must be between 2 and 50 characters')
    ])
    password = PasswordField('Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    password2 = PasswordField('Repeat Password', validators=[
        DataRequired(), 
        EqualTo('password', message='Passwords must match')
    ])
    role = SelectField('Role', choices=[
        (UserRole.PLAYER.value, 'Player'),
        (UserRole.ORGANIZER.value, 'Tournament Organizer')
    ], default=UserRole.PLAYER.value)
    submit = SubmitField('Register')

    def validate_username(self, username):
        """Check if username is already taken."""
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('Please use a different username.')

    def validate_email(self, email):
        """Check if email is already registered."""
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('Please use a different email address.')


class EditProfileForm(FlaskForm):
    """Edit user profile form."""
    first_name = StringField('First Name', validators=[
        DataRequired(),
        Length(min=2, max=50)
    ])
    last_name = StringField('Last Name', validators=[
        DataRequired(),
        Length(min=2, max=50)
    ])
    bio = TextAreaField('Bio', validators=[Length(max=500)])
    discord_username = StringField('Discord Username', validators=[Length(max=100)])
    twitch_username = StringField('Twitch Username', validators=[Length(max=100)])
    submit = SubmitField('Update Profile')


class ChangePasswordForm(FlaskForm):
    """Change password form."""
    current_password = PasswordField('Current Password', validators=[DataRequired()])
    new_password = PasswordField('New Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    new_password2 = PasswordField('Repeat New Password', validators=[
        DataRequired(),
        EqualTo('new_password', message='Passwords must match')
    ])
    submit = SubmitField('Change Password')


class PlayerProfileForm(FlaskForm):
    """Player profile setup form."""
    in_game_name = StringField('In-Game Name', validators=[
        DataRequired(),
        Length(min=2, max=100)
    ])
    preferred_games = StringField('Preferred Games', validators=[Length(max=255)],
                                description='Comma-separated list of games you play')
    skill_level = SelectField('Skill Level', choices=[
        ('Beginner', 'Beginner'),
        ('Intermediate', 'Intermediate'),
        ('Advanced', 'Advanced'),
        ('Professional', 'Professional')
    ], default='Intermediate')
    submit = SubmitField('Save Player Profile')
