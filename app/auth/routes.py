from flask import render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, current_user, login_required
from urllib.parse import urlparse
from app import db
from app.auth import bp
from app.auth.forms import LoginForm, RegistrationForm, EditProfileForm, ChangePasswordForm, PlayerProfileForm
from app.models import User, Player, UserRole


@bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login page."""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user is None or not user.check_password(form.password.data):
            flash('Invalid username or password', 'error')
            return redirect(url_for('auth.login'))

        login_user(user, remember=form.remember_me.data)
        next_page = request.args.get('next')
        if not next_page or urlparse(next_page).netloc != '':
            next_page = url_for('main.index')
        return redirect(next_page)

    return render_template('auth/login.html', title='Sign In', form=form)


@bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration page."""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            role=UserRole(form.role.data)
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()

        # Create player profile if user registered as player
        if user.role == UserRole.PLAYER:
            player = Player(user_id=user.id, in_game_name=user.username)
            db.session.add(player)
            db.session.commit()

        flash('Congratulations, you are now a registered user!', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/register.html', title='Register', form=form)


@bp.route('/logout')
@login_required
def logout():
    """User logout."""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('main.index'))


@bp.route('/profile')
@login_required
def profile():
    """User profile page."""
    return render_template('auth/profile.html', title='Profile', user=current_user)


@bp.route('/edit_profile', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """Edit user profile."""
    form = EditProfileForm()
    if form.validate_on_submit():
        current_user.first_name = form.first_name.data
        current_user.last_name = form.last_name.data
        current_user.bio = form.bio.data
        current_user.discord_username = form.discord_username.data
        current_user.twitch_username = form.twitch_username.data
        db.session.commit()
        flash('Your profile has been updated.', 'success')
        return redirect(url_for('auth.profile'))
    elif request.method == 'GET':
        form.first_name.data = current_user.first_name
        form.last_name.data = current_user.last_name
        form.bio.data = current_user.bio
        form.discord_username.data = current_user.discord_username
        form.twitch_username.data = current_user.twitch_username
    return render_template('auth/edit_profile.html', title='Edit Profile', form=form)


@bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password."""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('Current password is incorrect.', 'error')
            return redirect(url_for('auth.change_password'))

        current_user.set_password(form.new_password.data)
        db.session.commit()
        flash('Your password has been changed.', 'success')
        return redirect(url_for('auth.profile'))

    return render_template('auth/change_password.html', title='Change Password', form=form)


@bp.route('/player_profile', methods=['GET', 'POST'])
@login_required
def player_profile():
    """Setup or edit player profile."""
    if current_user.role != UserRole.PLAYER:
        flash('Only players can access this page.', 'error')
        return redirect(url_for('main.index'))

    player = current_user.player_profile
    if not player:
        # Create player profile if it doesn't exist
        player = Player(user_id=current_user.id, in_game_name=current_user.username)
        db.session.add(player)
        db.session.commit()

    form = PlayerProfileForm()
    if form.validate_on_submit():
        player.in_game_name = form.in_game_name.data
        player.preferred_games = form.preferred_games.data
        player.skill_level = form.skill_level.data
        db.session.commit()
        flash('Your player profile has been updated.', 'success')
        return redirect(url_for('auth.profile'))
    elif request.method == 'GET':
        form.in_game_name.data = player.in_game_name
        form.preferred_games.data = player.preferred_games
        form.skill_level.data = player.skill_level

    return render_template('auth/player_profile.html', title='Player Profile', form=form)
