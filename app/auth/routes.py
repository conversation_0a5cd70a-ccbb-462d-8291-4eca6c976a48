from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_user, logout_user, current_user, login_required
from urllib.parse import urlparse
from app import db, mongo
from app.auth import bp
from app.auth.forms import LoginForm, RegistrationForm, EditProfileForm, ChangePasswordForm, PlayerProfileForm
from app.models import User, Player, UserRole
from app.mongo_models import User<PERSON>ole as MongoUserRole
from bson import ObjectId


@bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login page."""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = LoginForm()
    if form.validate_on_submit():
        # Use MongoDB to find user
        user_model = current_app.mongo_models['User']
        user_doc = user_model.find_by_username(form.username.data)

        if user_doc is None or not user_model.check_password(user_doc['password_hash'], form.password.data):
            flash('Invalid username or password', 'error')
            return redirect(url_for('auth.login'))

        # Create user object for Flask-Login
        class MongoUser:
            def __init__(self, user_doc):
                self._id = user_doc['_id']
                self.username = user_doc['username']
                self.email = user_doc['email']
                self.password_hash = user_doc['password_hash']
                self.first_name = user_doc['first_name']
                self.last_name = user_doc['last_name']
                self.role = user_doc['role']
                self.is_active = user_doc['is_active']
                self.created_at = user_doc['created_at']
                self.updated_at = user_doc.get('updated_at')
                self.avatar_url = user_doc.get('avatar_url')
                self.bio = user_doc.get('bio')
                self.discord_username = user_doc.get('discord_username')
                self.twitch_username = user_doc.get('twitch_username')

            def get_id(self):
                return str(self._id)

            @property
            def full_name(self):
                return f"{self.first_name} {self.last_name}"

            def check_password(self, password):
                return user_model.check_password(self.password_hash, password)

            def is_authenticated(self):
                return True

            def is_anonymous(self):
                return False

        user = MongoUser(user_doc)
        login_user(user, remember=form.remember_me.data)
        next_page = request.args.get('next')
        if not next_page or urlparse(next_page).netloc != '':
            next_page = url_for('main.index')
        return redirect(next_page)

    return render_template('auth/login.html', title='Sign In', form=form)


@bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration page."""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = RegistrationForm()
    if form.validate_on_submit():
        # Use MongoDB to create user
        user_model = current_app.mongo_models['User']
        player_model = current_app.mongo_models['Player']

        try:
            # Create user in MongoDB
            user_result = user_model.create_user(
                username=form.username.data,
                email=form.email.data,
                password=form.password.data,
                first_name=form.first_name.data,
                last_name=form.last_name.data,
                role=form.role.data
            )

            # Create player profile if user registered as player
            if form.role.data == MongoUserRole.PLAYER.value:
                player_model.create_player(
                    user_id=user_result.inserted_id,
                    in_game_name=form.username.data
                )

            flash('Congratulations, you are now a registered user!', 'success')
            return redirect(url_for('auth.login'))

        except Exception as e:
            flash(f'Registration failed: {str(e)}', 'error')
            return redirect(url_for('auth.register'))

    return render_template('auth/register.html', title='Register', form=form)


@bp.route('/logout')
@login_required
def logout():
    """User logout."""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('main.index'))


@bp.route('/profile')
@login_required
def profile():
    """User profile page."""
    return render_template('auth/profile.html', title='Profile', user=current_user)


@bp.route('/edit_profile', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """Edit user profile."""
    form = EditProfileForm()
    if form.validate_on_submit():
        current_user.first_name = form.first_name.data
        current_user.last_name = form.last_name.data
        current_user.bio = form.bio.data
        current_user.discord_username = form.discord_username.data
        current_user.twitch_username = form.twitch_username.data
        db.session.commit()
        flash('Your profile has been updated.', 'success')
        return redirect(url_for('auth.profile'))
    elif request.method == 'GET':
        form.first_name.data = current_user.first_name
        form.last_name.data = current_user.last_name
        form.bio.data = current_user.bio
        form.discord_username.data = current_user.discord_username
        form.twitch_username.data = current_user.twitch_username
    return render_template('auth/edit_profile.html', title='Edit Profile', form=form)


@bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password."""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('Current password is incorrect.', 'error')
            return redirect(url_for('auth.change_password'))

        current_user.set_password(form.new_password.data)
        db.session.commit()
        flash('Your password has been changed.', 'success')
        return redirect(url_for('auth.profile'))

    return render_template('auth/change_password.html', title='Change Password', form=form)


@bp.route('/player_profile', methods=['GET', 'POST'])
@login_required
def player_profile():
    """Setup or edit player profile."""
    if current_user.role != UserRole.PLAYER:
        flash('Only players can access this page.', 'error')
        return redirect(url_for('main.index'))

    player = current_user.player_profile
    if not player:
        # Create player profile if it doesn't exist
        player = Player(user_id=current_user.id, in_game_name=current_user.username)
        db.session.add(player)
        db.session.commit()

    form = PlayerProfileForm()
    if form.validate_on_submit():
        player.in_game_name = form.in_game_name.data
        player.preferred_games = form.preferred_games.data
        player.skill_level = form.skill_level.data
        db.session.commit()
        flash('Your player profile has been updated.', 'success')
        return redirect(url_for('auth.profile'))
    elif request.method == 'GET':
        form.in_game_name.data = player.in_game_name
        form.preferred_games.data = player.preferred_games
        form.skill_level.data = player.skill_level

    return render_template('auth/player_profile.html', title='Player Profile', form=form)
