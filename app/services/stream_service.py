"""
Stream embedding and management service for Twitch/YouTube integration.
"""
import re
import requests
from urllib.parse import urlparse, parse_qs
from typing import Dict, Optional, List
from app.models import Tournament, Match, Stream
from app import db
import logging

logger = logging.getLogger(__name__)


class StreamService:
    """Service for managing tournament and match streams."""
    
    def __init__(self):
        self.twitch_client_id = None
        self.twitch_client_secret = None
        self.youtube_api_key = None
    
    def init_app(self, app):
        """Initialize with Flask app configuration."""
        self.twitch_client_id = app.config.get('TWITCH_CLIENT_ID')
        self.twitch_client_secret = app.config.get('TWITCH_CLIENT_SECRET')
        self.youtube_api_key = app.config.get('YOUTUBE_API_KEY')
    
    def parse_stream_url(self, url: str) -> Dict[str, str]:
        """Parse stream URL and extract platform and stream ID."""
        if not url:
            return {'platform': None, 'stream_id': None, 'embed_url': None}
        
        # Normalize URL
        url = url.strip()
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Twitch URLs
        if 'twitch.tv' in domain:
            # Extract channel name from path
            path_parts = parsed.path.strip('/').split('/')
            if path_parts and path_parts[0]:
                channel_name = path_parts[0]
                return {
                    'platform': 'twitch',
                    'stream_id': channel_name,
                    'embed_url': f'https://player.twitch.tv/?channel={channel_name}&parent=localhost&parent=127.0.0.1'
                }
        
        # YouTube URLs
        elif 'youtube.com' in domain or 'youtu.be' in domain:
            video_id = None
            
            if 'youtu.be' in domain:
                # Short URL format: https://youtu.be/VIDEO_ID
                video_id = parsed.path.strip('/')
            elif 'youtube.com' in domain:
                if '/watch' in parsed.path:
                    # Standard URL: https://www.youtube.com/watch?v=VIDEO_ID
                    query_params = parse_qs(parsed.query)
                    video_id = query_params.get('v', [None])[0]
                elif '/embed/' in parsed.path:
                    # Embed URL: https://www.youtube.com/embed/VIDEO_ID
                    video_id = parsed.path.split('/embed/')[-1].split('?')[0]
                elif '/live/' in parsed.path:
                    # Live URL: https://www.youtube.com/live/VIDEO_ID
                    video_id = parsed.path.split('/live/')[-1].split('?')[0]
            
            if video_id:
                return {
                    'platform': 'youtube',
                    'stream_id': video_id,
                    'embed_url': f'https://www.youtube.com/embed/{video_id}?autoplay=1&mute=1'
                }
        
        return {'platform': 'unknown', 'stream_id': None, 'embed_url': url}
    
    def create_tournament_stream(self, tournament_id: int, stream_url: str, 
                               title: str = None, description: str = None) -> Optional[Stream]:
        """Create a stream for a tournament."""
        try:
            tournament = Tournament.query.get(tournament_id)
            if not tournament:
                logger.error(f'Tournament {tournament_id} not found')
                return None
            
            stream_info = self.parse_stream_url(stream_url)
            if not stream_info['platform'] or stream_info['platform'] == 'unknown':
                logger.error(f'Invalid stream URL: {stream_url}')
                return None
            
            # Check if stream already exists for this tournament
            existing_stream = Stream.query.filter_by(
                tournament_id=tournament_id,
                platform=stream_info['platform'],
                stream_id=stream_info['stream_id']
            ).first()
            
            if existing_stream:
                # Update existing stream
                existing_stream.stream_url = stream_url
                existing_stream.embed_url = stream_info['embed_url']
                existing_stream.title = title or existing_stream.title
                existing_stream.description = description or existing_stream.description
                existing_stream.is_active = True
                db.session.commit()
                return existing_stream
            
            # Create new stream
            stream = Stream(
                tournament_id=tournament_id,
                platform=stream_info['platform'],
                stream_id=stream_info['stream_id'],
                stream_url=stream_url,
                embed_url=stream_info['embed_url'],
                title=title or f"{tournament.name} - Live Stream",
                description=description,
                is_active=True
            )
            
            db.session.add(stream)
            db.session.commit()
            
            logger.info(f'Created stream for tournament {tournament_id}: {stream_info["platform"]}')
            return stream
            
        except Exception as e:
            logger.error(f'Error creating tournament stream: {str(e)}')
            db.session.rollback()
            return None
    
    def create_match_stream(self, match_id: int, stream_url: str, 
                          title: str = None, description: str = None) -> Optional[Stream]:
        """Create a stream for a specific match."""
        try:
            match = Match.query.get(match_id)
            if not match:
                logger.error(f'Match {match_id} not found')
                return None
            
            stream_info = self.parse_stream_url(stream_url)
            if not stream_info['platform'] or stream_info['platform'] == 'unknown':
                logger.error(f'Invalid stream URL: {stream_url}')
                return None
            
            # Check if stream already exists for this match
            existing_stream = Stream.query.filter_by(
                match_id=match_id,
                platform=stream_info['platform'],
                stream_id=stream_info['stream_id']
            ).first()
            
            if existing_stream:
                # Update existing stream
                existing_stream.stream_url = stream_url
                existing_stream.embed_url = stream_info['embed_url']
                existing_stream.title = title or existing_stream.title
                existing_stream.description = description or existing_stream.description
                existing_stream.is_active = True
                db.session.commit()
                return existing_stream
            
            # Create new stream
            stream = Stream(
                tournament_id=match.tournament_id,
                match_id=match_id,
                platform=stream_info['platform'],
                stream_id=stream_info['stream_id'],
                stream_url=stream_url,
                embed_url=stream_info['embed_url'],
                title=title or f"{match.teams_display} - Live Stream",
                description=description,
                is_active=True
            )
            
            db.session.add(stream)
            db.session.commit()
            
            logger.info(f'Created stream for match {match_id}: {stream_info["platform"]}')
            return stream
            
        except Exception as e:
            logger.error(f'Error creating match stream: {str(e)}')
            db.session.rollback()
            return None
    
    def get_tournament_streams(self, tournament_id: int, active_only: bool = True) -> List[Stream]:
        """Get all streams for a tournament."""
        query = Stream.query.filter_by(tournament_id=tournament_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.order_by(Stream.created_at.desc()).all()
    
    def get_match_streams(self, match_id: int, active_only: bool = True) -> List[Stream]:
        """Get all streams for a specific match."""
        query = Stream.query.filter_by(match_id=match_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.order_by(Stream.created_at.desc()).all()
    
    def deactivate_stream(self, stream_id: int) -> bool:
        """Deactivate a stream."""
        try:
            stream = Stream.query.get(stream_id)
            if stream:
                stream.is_active = False
                db.session.commit()
                logger.info(f'Deactivated stream {stream_id}')
                return True
            return False
        except Exception as e:
            logger.error(f'Error deactivating stream: {str(e)}')
            db.session.rollback()
            return False
    
    def delete_stream(self, stream_id: int) -> bool:
        """Delete a stream."""
        try:
            stream = Stream.query.get(stream_id)
            if stream:
                db.session.delete(stream)
                db.session.commit()
                logger.info(f'Deleted stream {stream_id}')
                return True
            return False
        except Exception as e:
            logger.error(f'Error deleting stream: {str(e)}')
            db.session.rollback()
            return False
    
    def validate_twitch_channel(self, channel_name: str) -> bool:
        """Validate if a Twitch channel exists (requires API key)."""
        if not self.twitch_client_id or not self.twitch_client_secret:
            logger.warning('Twitch API credentials not configured')
            return True  # Assume valid if no API access
        
        try:
            # Get OAuth token
            auth_response = requests.post('https://id.twitch.tv/oauth2/token', {
                'client_id': self.twitch_client_id,
                'client_secret': self.twitch_client_secret,
                'grant_type': 'client_credentials'
            })
            
            if auth_response.status_code != 200:
                logger.error('Failed to get Twitch OAuth token')
                return True  # Assume valid if API fails
            
            access_token = auth_response.json()['access_token']
            
            # Check if channel exists
            headers = {
                'Client-ID': self.twitch_client_id,
                'Authorization': f'Bearer {access_token}'
            }
            
            response = requests.get(
                f'https://api.twitch.tv/helix/users?login={channel_name}',
                headers=headers
            )
            
            return response.status_code == 200 and len(response.json().get('data', [])) > 0
            
        except Exception as e:
            logger.error(f'Error validating Twitch channel: {str(e)}')
            return True  # Assume valid if validation fails
    
    def validate_youtube_video(self, video_id: str) -> bool:
        """Validate if a YouTube video exists (requires API key)."""
        if not self.youtube_api_key:
            logger.warning('YouTube API key not configured')
            return True  # Assume valid if no API access
        
        try:
            response = requests.get(
                'https://www.googleapis.com/youtube/v3/videos',
                params={
                    'id': video_id,
                    'key': self.youtube_api_key,
                    'part': 'id'
                }
            )
            
            return response.status_code == 200 and len(response.json().get('items', [])) > 0
            
        except Exception as e:
            logger.error(f'Error validating YouTube video: {str(e)}')
            return True  # Assume valid if validation fails
    
    def get_stream_embed_html(self, stream: Stream, width: int = 800, height: int = 450) -> str:
        """Generate HTML for embedding a stream."""
        if not stream or not stream.embed_url:
            return ''
        
        if stream.platform == 'twitch':
            return f'''
            <iframe
                src="{stream.embed_url}"
                height="{height}"
                width="{width}"
                allowfullscreen="true"
                scrolling="no"
                frameborder="0">
            </iframe>
            '''
        elif stream.platform == 'youtube':
            return f'''
            <iframe
                width="{width}"
                height="{height}"
                src="{stream.embed_url}"
                title="{stream.title}"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen>
            </iframe>
            '''
        else:
            # Generic iframe for other platforms
            return f'''
            <iframe
                src="{stream.embed_url}"
                width="{width}"
                height="{height}"
                frameborder="0"
                allowfullscreen>
            </iframe>
            '''


# Global service instance
stream_service = StreamService()
