"""Leaderboard and statistics service for tournaments."""
from typing import List, Dict, Any, Optional
from sqlalchemy import func, desc, asc
from app.models import Tournament, TournamentTeam, Match, Team, Game, TournamentFormat, MatchStatus
from app import db


class LeaderboardService:
    """Service for calculating leaderboards and statistics."""
    
    def __init__(self, tournament: Tournament):
        self.tournament = tournament
    
    def get_tournament_leaderboard(self) -> List[Dict[str, Any]]:
        """Get tournament leaderboard with rankings and statistics."""
        if self.tournament.format == TournamentFormat.ROUND_ROBIN:
            return self._get_round_robin_leaderboard()
        elif self.tournament.format == TournamentFormat.SWISS:
            return self._get_swiss_leaderboard()
        else:
            return self._get_elimination_leaderboard()
    
    def _get_round_robin_leaderboard(self) -> List[Dict[str, Any]]:
        """Calculate round robin leaderboard based on points."""
        leaderboard = []
        
        # Get all teams in tournament
        tournament_teams = TournamentTeam.query.filter_by(
            tournament_id=self.tournament.id, is_active=True
        ).all()
        
        for tournament_team in tournament_teams:
            team = tournament_team.team
            stats = self._calculate_team_stats(team.id)
            
            # Round robin scoring: 3 points for win, 1 for draw, 0 for loss
            points = stats['wins'] * 3 + stats['draws'] * 1
            
            leaderboard.append({
                'rank': 0,  # Will be calculated after sorting
                'team_id': team.id,
                'team_name': team.name,
                'team_tag': team.tag,
                'placement': tournament_team.placement,
                'seed': tournament_team.seed,
                'points': points,
                'matches_played': stats['matches_played'],
                'wins': stats['wins'],
                'losses': stats['losses'],
                'draws': stats['draws'],
                'games_won': stats['games_won'],
                'games_lost': stats['games_lost'],
                'game_differential': stats['games_won'] - stats['games_lost'],
                'win_percentage': stats['win_percentage']
            })
        
        # Sort by points, then by game differential, then by games won
        leaderboard.sort(key=lambda x: (
            -x['points'],
            -x['game_differential'],
            -x['games_won']
        ))
        
        # Assign ranks
        for i, entry in enumerate(leaderboard):
            entry['rank'] = i + 1
        
        return leaderboard
    
    def _get_swiss_leaderboard(self) -> List[Dict[str, Any]]:
        """Calculate Swiss tournament leaderboard."""
        leaderboard = []
        
        tournament_teams = TournamentTeam.query.filter_by(
            tournament_id=self.tournament.id, is_active=True
        ).all()
        
        for tournament_team in tournament_teams:
            team = tournament_team.team
            stats = self._calculate_team_stats(team.id)
            
            # Swiss scoring: 1 point per win
            points = stats['wins']
            
            leaderboard.append({
                'rank': 0,
                'team_id': team.id,
                'team_name': team.name,
                'team_tag': team.tag,
                'placement': tournament_team.placement,
                'seed': tournament_team.seed,
                'points': points,
                'matches_played': stats['matches_played'],
                'wins': stats['wins'],
                'losses': stats['losses'],
                'draws': stats['draws'],
                'games_won': stats['games_won'],
                'games_lost': stats['games_lost'],
                'game_differential': stats['games_won'] - stats['games_lost'],
                'win_percentage': stats['win_percentage'],
                'buchholz_score': self._calculate_buchholz_score(team.id)
            })
        
        # Sort by points, then by Buchholz score, then by game differential
        leaderboard.sort(key=lambda x: (
            -x['points'],
            -x['buchholz_score'],
            -x['game_differential']
        ))
        
        for i, entry in enumerate(leaderboard):
            entry['rank'] = i + 1
        
        return leaderboard
    
    def _get_elimination_leaderboard(self) -> List[Dict[str, Any]]:
        """Calculate elimination tournament leaderboard based on placement."""
        leaderboard = []
        
        tournament_teams = TournamentTeam.query.filter_by(
            tournament_id=self.tournament.id, is_active=True
        ).order_by(TournamentTeam.placement.asc().nullslast()).all()
        
        for tournament_team in tournament_teams:
            team = tournament_team.team
            stats = self._calculate_team_stats(team.id)
            
            leaderboard.append({
                'rank': tournament_team.placement or 999,
                'team_id': team.id,
                'team_name': team.name,
                'team_tag': team.tag,
                'placement': tournament_team.placement,
                'seed': tournament_team.seed,
                'points': 0,  # Not applicable for elimination
                'matches_played': stats['matches_played'],
                'wins': stats['wins'],
                'losses': stats['losses'],
                'draws': stats['draws'],
                'games_won': stats['games_won'],
                'games_lost': stats['games_lost'],
                'game_differential': stats['games_won'] - stats['games_lost'],
                'win_percentage': stats['win_percentage']
            })
        
        # Sort by placement (lower is better), then by wins
        leaderboard.sort(key=lambda x: (
            x['placement'] if x['placement'] else 999,
            -x['wins']
        ))
        
        return leaderboard
    
    def _calculate_team_stats(self, team_id: int) -> Dict[str, Any]:
        """Calculate comprehensive statistics for a team."""
        # Get all matches for this team in this tournament
        matches = Match.query.filter(
            Match.tournament_id == self.tournament.id,
            Match.status == MatchStatus.COMPLETED,
            db.or_(
                Match.team1_id == team_id,
                Match.team2_id == team_id
            )
        ).all()
        
        wins = 0
        losses = 0
        draws = 0
        games_won = 0
        games_lost = 0
        
        for match in matches:
            if match.winner_id == team_id:
                wins += 1
            elif match.winner_id is None:
                draws += 1
            else:
                losses += 1
            
            # Calculate game scores
            if match.team1_id == team_id:
                games_won += match.team1_score or 0
                games_lost += match.team2_score or 0
            else:
                games_won += match.team2_score or 0
                games_lost += match.team1_score or 0
        
        matches_played = len(matches)
        win_percentage = (wins / matches_played * 100) if matches_played > 0 else 0
        
        return {
            'matches_played': matches_played,
            'wins': wins,
            'losses': losses,
            'draws': draws,
            'games_won': games_won,
            'games_lost': games_lost,
            'win_percentage': round(win_percentage, 1)
        }
    
    def _calculate_buchholz_score(self, team_id: int) -> float:
        """Calculate Buchholz score for Swiss tournaments (sum of opponents' scores)."""
        # Get all opponents this team has faced
        matches = Match.query.filter(
            Match.tournament_id == self.tournament.id,
            Match.status == MatchStatus.COMPLETED,
            db.or_(
                Match.team1_id == team_id,
                Match.team2_id == team_id
            )
        ).all()
        
        opponent_scores = []
        for match in matches:
            opponent_id = match.team2_id if match.team1_id == team_id else match.team1_id
            if opponent_id:
                opponent_stats = self._calculate_team_stats(opponent_id)
                opponent_scores.append(opponent_stats['wins'])
        
        return sum(opponent_scores)
    
    def get_tournament_statistics(self) -> Dict[str, Any]:
        """Get comprehensive tournament statistics."""
        total_matches = Match.query.filter_by(tournament_id=self.tournament.id).count()
        completed_matches = Match.query.filter_by(
            tournament_id=self.tournament.id, 
            status=MatchStatus.COMPLETED
        ).count()
        
        # Get total games played
        total_games = Game.query.join(Match).filter(
            Match.tournament_id == self.tournament.id
        ).count()
        
        # Calculate average match duration
        avg_duration_query = db.session.query(func.avg(Match.duration)).filter(
            Match.tournament_id == self.tournament.id,
            Match.duration.isnot(None)
        ).scalar()
        
        avg_duration = round(avg_duration_query, 1) if avg_duration_query else 0
        
        # Get most active teams (most matches played)
        most_active = db.session.query(
            Team.name,
            func.count(Match.id).label('match_count')
        ).join(TournamentTeam).join(Match, db.or_(
            Match.team1_id == TournamentTeam.team_id,
            Match.team2_id == TournamentTeam.team_id
        )).filter(
            TournamentTeam.tournament_id == self.tournament.id,
            Match.status == MatchStatus.COMPLETED
        ).group_by(Team.id, Team.name).order_by(desc('match_count')).limit(5).all()
        
        return {
            'total_teams': self.tournament.registered_teams_count,
            'total_matches': total_matches,
            'completed_matches': completed_matches,
            'completion_percentage': round((completed_matches / total_matches * 100), 1) if total_matches > 0 else 0,
            'total_games': total_games,
            'average_match_duration': avg_duration,
            'most_active_teams': [{'name': name, 'matches': count} for name, count in most_active]
        }
    
    def get_player_statistics(self) -> List[Dict[str, Any]]:
        """Get individual player statistics (if player data is tracked)."""
        # This would require additional player-match tracking
        # For now, return empty list as we're focusing on team statistics
        return []

    def update_tournament_placements(self):
        """Update tournament team placements based on current leaderboard."""
        leaderboard = self.get_tournament_leaderboard()

        for entry in leaderboard:
            tournament_team = TournamentTeam.query.filter_by(
                tournament_id=self.tournament.id,
                team_id=entry['team_id']
            ).first()

            if tournament_team:
                tournament_team.placement = entry['rank']

        db.session.commit()
        return leaderboard
