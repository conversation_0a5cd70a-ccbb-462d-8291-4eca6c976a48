"""
Match scheduling service for tournament management.
"""
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from app.models import Tournament, Match, MatchStatus, TournamentStatus
from app.services.bracket_generator import BracketGenerator
from app import db


class MatchScheduler:
    """Service for scheduling tournament matches."""
    
    def __init__(self, tournament: Tournament):
        self.tournament = tournament
    
    def generate_and_schedule_bracket(self, start_time: Optional[datetime] = None) -> List[Match]:
        """Generate bracket and schedule all matches."""
        if start_time is None:
            start_time = self.tournament.tournament_start
        
        # Generate bracket
        generator = BracketGenerator(self.tournament)
        matches = generator.generate_bracket()
        
        # Schedule matches
        generator.schedule_matches(matches, start_time)
        
        # Save matches to database
        for match in matches:
            db.session.add(match)
        
        try:
            db.session.commit()
            return matches
        except Exception as e:
            db.session.rollback()
            raise e
    
    def reschedule_match(self, match: Match, new_time: datetime) -> bool:
        """Reschedule a specific match."""
        try:
            match.scheduled_time = new_time
            db.session.commit()
            return True
        except Exception:
            db.session.rollback()
            return False
    
    def advance_bracket(self, completed_match: Match) -> List[Match]:
        """Advance bracket after a match is completed."""
        if not completed_match.is_completed or not completed_match.winner_id:
            raise ValueError("Match must be completed with a winner to advance bracket")
        
        new_matches = []
        
        if self.tournament.format.value == 'single_elimination':
            new_matches = self._advance_single_elimination(completed_match)
        elif self.tournament.format.value == 'double_elimination':
            new_matches = self._advance_double_elimination(completed_match)
        elif self.tournament.format.value == 'swiss':
            new_matches = self._advance_swiss(completed_match)
        # Round robin doesn't need advancement
        
        # Save new matches
        for match in new_matches:
            db.session.add(match)
        
        try:
            db.session.commit()
            return new_matches
        except Exception as e:
            db.session.rollback()
            raise e
    
    def _advance_single_elimination(self, completed_match: Match) -> List[Match]:
        """Advance single elimination bracket."""
        new_matches = []
        
        # Find the next match this winner should advance to
        next_round = completed_match.round_number + 1
        next_match_number = (completed_match.match_number + 1) // 2
        
        # Check if next match already exists
        next_match = Match.query.filter_by(
            tournament_id=self.tournament.id,
            round_number=next_round,
            match_number=next_match_number
        ).first()
        
        if next_match:
            # Determine if winner goes to team1 or team2 slot
            if completed_match.match_number % 2 == 1:  # Odd match number -> team1
                next_match.team1_id = completed_match.winner_id
            else:  # Even match number -> team2
                next_match.team2_id = completed_match.winner_id
            
            # If both teams are now set, the match is ready
            if next_match.team1_id and next_match.team2_id:
                # Schedule the match
                self._schedule_next_round_match(next_match, completed_match)
        
        return new_matches
    
    def _advance_double_elimination(self, completed_match: Match) -> List[Match]:
        """Advance double elimination bracket."""
        new_matches = []
        
        # This is a simplified version - real double elimination is more complex
        if completed_match.bracket_position.startswith('WB'):
            # Winner advances in winners bracket
            # Loser drops to losers bracket
            pass
        elif completed_match.bracket_position.startswith('LB'):
            # Winner advances in losers bracket
            # Loser is eliminated
            pass
        
        return new_matches
    
    def _advance_swiss(self, completed_match: Match) -> List[Match]:
        """Check if Swiss round is complete and generate next round."""
        new_matches = []
        
        # Check if all matches in current round are complete
        current_round = completed_match.round_number
        round_matches = Match.query.filter_by(
            tournament_id=self.tournament.id,
            round_number=current_round
        ).all()
        
        all_complete = all(match.status == MatchStatus.COMPLETED for match in round_matches)
        
        if all_complete:
            # Generate next round
            from app.services.bracket_generator import generate_next_swiss_round
            next_round_matches = generate_next_swiss_round(self.tournament, current_round + 1)
            
            # Schedule next round matches
            if next_round_matches:
                start_time = max(match.completed_at for match in round_matches if match.completed_at)
                start_time += timedelta(minutes=30)  # 30 minute break between rounds
                
                for i, match in enumerate(next_round_matches):
                    match.scheduled_time = start_time + timedelta(minutes=i * 15)  # Stagger by 15 minutes
                
                new_matches.extend(next_round_matches)
        
        return new_matches
    
    def _schedule_next_round_match(self, next_match: Match, completed_match: Match) -> None:
        """Schedule a next round match based on completed match."""
        if completed_match.completed_at:
            # Schedule 30 minutes after the completed match
            next_match.scheduled_time = completed_match.completed_at + timedelta(minutes=30)
        else:
            # Fallback to scheduled time + estimated duration
            estimated_end = completed_match.scheduled_time + timedelta(hours=1)
            next_match.scheduled_time = estimated_end + timedelta(minutes=30)
    
    def get_tournament_schedule(self) -> Dict[str, List[Match]]:
        """Get organized tournament schedule."""
        matches = Match.query.filter_by(tournament_id=self.tournament.id).order_by(
            Match.round_number, Match.match_number
        ).all()
        
        schedule = {}
        for match in matches:
            round_key = f"Round {match.round_number}"
            if match.bracket_position:
                if match.bracket_position.startswith('WB'):
                    round_key = f"Winners Bracket - Round {match.round_number}"
                elif match.bracket_position.startswith('LB'):
                    round_key = f"Losers Bracket - Round {match.round_number - 100}"
                elif match.bracket_position.startswith('GF'):
                    round_key = "Grand Final"
                elif match.bracket_position.startswith('RR'):
                    round_key = "Round Robin"
                elif match.bracket_position.startswith('SW'):
                    round_key = f"Swiss Round {match.round_number}"
            
            if round_key not in schedule:
                schedule[round_key] = []
            schedule[round_key].append(match)
        
        return schedule
    
    def get_next_matches(self, limit: int = 5) -> List[Match]:
        """Get next scheduled matches."""
        return Match.query.filter(
            Match.tournament_id == self.tournament.id,
            Match.status == MatchStatus.SCHEDULED,
            Match.scheduled_time.isnot(None)
        ).order_by(Match.scheduled_time).limit(limit).all()
    
    def get_live_matches(self) -> List[Match]:
        """Get currently live matches."""
        return Match.query.filter(
            Match.tournament_id == self.tournament.id,
            Match.status == MatchStatus.IN_PROGRESS
        ).all()
    
    def start_tournament(self) -> bool:
        """Start the tournament by generating bracket and updating status."""
        try:
            # Generate and schedule bracket
            matches = self.generate_and_schedule_bracket()
            
            # Update tournament status
            self.tournament.status = TournamentStatus.IN_PROGRESS
            
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            raise e
    
    def check_tournament_completion(self) -> bool:
        """Check if tournament is complete."""
        # Get all matches
        matches = Match.query.filter_by(tournament_id=self.tournament.id).all()
        
        if not matches:
            return False
        
        # Check if all matches are completed
        all_completed = all(match.status in [MatchStatus.COMPLETED, MatchStatus.CANCELLED] for match in matches)
        
        if all_completed and self.tournament.status == TournamentStatus.IN_PROGRESS:
            # Tournament is complete
            self.tournament.status = TournamentStatus.COMPLETED
            self.tournament.tournament_end = datetime.utcnow()
            
            # Calculate final placements
            self._calculate_final_placements()
            
            try:
                db.session.commit()
                return True
            except Exception:
                db.session.rollback()
                return False
        
        return False
    
    def _calculate_final_placements(self) -> None:
        """Calculate final tournament placements."""
        if self.tournament.format == TournamentFormat.ROUND_ROBIN:
            self._calculate_round_robin_placements()
        elif self.tournament.format == TournamentFormat.SWISS:
            self._calculate_swiss_placements()
        else:
            self._calculate_elimination_placements()
    
    def _calculate_round_robin_placements(self) -> None:
        """Calculate placements for round robin tournament."""
        teams = list(self.tournament.teams.filter_by(is_active=True).all())
        team_stats = {}
        
        for team in teams:
            wins = 0
            losses = 0
            
            team_matches = Match.query.filter(
                Match.tournament_id == self.tournament.id,
                Match.status == MatchStatus.COMPLETED,
                (Match.team1_id == team.id) | (Match.team2_id == team.id)
            ).all()
            
            for match in team_matches:
                if match.winner_id == team.id:
                    wins += 1
                else:
                    losses += 1
            
            team_stats[team.id] = {'team': team, 'wins': wins, 'losses': losses}
        
        # Sort by wins (descending)
        sorted_teams = sorted(team_stats.values(), key=lambda x: x['wins'], reverse=True)
        
        # Assign placements
        for i, team_data in enumerate(sorted_teams):
            team_data['team'].placement = i + 1
    
    def _calculate_swiss_placements(self) -> None:
        """Calculate placements for Swiss tournament."""
        # Similar to round robin
        self._calculate_round_robin_placements()
    
    def _calculate_elimination_placements(self) -> None:
        """Calculate placements for elimination tournaments."""
        # Find the final match
        final_match = Match.query.filter(
            Match.tournament_id == self.tournament.id,
            Match.status == MatchStatus.COMPLETED
        ).order_by(Match.round_number.desc(), Match.match_number.desc()).first()
        
        if final_match and final_match.winner_id:
            # Winner gets 1st place
            winner_team = TournamentTeam.query.get(final_match.winner_id)
            if winner_team:
                winner_team.placement = 1
            
            # Runner-up gets 2nd place
            loser_id = final_match.team1_id if final_match.winner_id == final_match.team2_id else final_match.team2_id
            if loser_id:
                loser_team = TournamentTeam.query.get(loser_id)
                if loser_team:
                    loser_team.placement = 2
