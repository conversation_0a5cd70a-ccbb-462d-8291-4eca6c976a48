"""Team service for handling team-related business logic and statistics."""

from typing import Dict, Any, List, Optional
from sqlalchemy import or_, func, desc
from app.models import Team, Player, Tournament, TournamentTeam, Match, MatchStatus
from app import db


class TeamService:
    """Service class for team-related operations."""
    
    @staticmethod
    def get_team_statistics(team: Team) -> Dict[str, Any]:
        """Calculate comprehensive statistics for a team."""
        # Get tournament registrations
        tournament_registrations = team.tournament_registrations.filter_by(is_active=True).all()
        
        # Initialize statistics
        stats = {
            'total_tournaments': len(tournament_registrations),
            'total_matches': 0,
            'total_wins': 0,
            'total_losses': 0,
            'total_draws': 0,
            'games_won': 0,
            'games_lost': 0,
            'win_rate': 0.0,
            'recent_matches': [],
            'tournament_placements': [],
            'active_tournaments': 0,
            'completed_tournaments': 0
        }
        
        if not tournament_registrations:
            return stats
        
        # Calculate match statistics
        for registration in tournament_registrations:
            # Get matches for this team in this tournament
            team_matches = Match.query.filter(
                Match.tournament_id == registration.tournament_id,
                Match.status == MatchStatus.COMPLETED,
                or_(
                    Match.team1_id == registration.id,
                    Match.team2_id == registration.id
                )
            ).all()
            
            stats['total_matches'] += len(team_matches)
            
            # Count wins, losses, and draws
            for match in team_matches:
                if match.winner_id == registration.id:
                    stats['total_wins'] += 1
                elif match.winner_id is None:
                    stats['total_draws'] += 1
                else:
                    stats['total_losses'] += 1
                
                # Calculate game scores
                if match.team1_id == registration.id:
                    stats['games_won'] += match.team1_score or 0
                    stats['games_lost'] += match.team2_score or 0
                else:
                    stats['games_won'] += match.team2_score or 0
                    stats['games_lost'] += match.team1_score or 0
            
            # Track tournament status and placements
            if registration.placement:
                stats['tournament_placements'].append({
                    'tournament_name': registration.tournament.name,
                    'placement': registration.placement,
                    'prize_won': float(registration.prize_won) if registration.prize_won else 0
                })
                stats['completed_tournaments'] += 1
            else:
                # Check if tournament is still active
                if registration.tournament.status.value in ['registration_open', 'in_progress']:
                    stats['active_tournaments'] += 1
        
        # Calculate win rate
        if stats['total_matches'] > 0:
            stats['win_rate'] = round((stats['total_wins'] / stats['total_matches']) * 100, 1)
        
        # Get recent matches (last 5)
        stats['recent_matches'] = TeamService._get_recent_matches(team, limit=5)
        
        # Sort tournament placements by placement (best first)
        stats['tournament_placements'].sort(key=lambda x: x['placement'])
        
        return stats
    
    @staticmethod
    def _get_recent_matches(team: Team, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent matches for a team."""
        # Get all tournament registrations for this team
        registration_ids = [reg.id for reg in team.tournament_registrations.filter_by(is_active=True).all()]
        
        if not registration_ids:
            return []
        
        # Get recent matches
        recent_matches = Match.query.filter(
            Match.status == MatchStatus.COMPLETED,
            or_(
                Match.team1_id.in_(registration_ids),
                Match.team2_id.in_(registration_ids)
            )
        ).order_by(desc(Match.completed_at)).limit(limit).all()
        
        matches_data = []
        for match in recent_matches:
            # Determine if this team won
            team_registration_id = None
            opponent_name = "Unknown"
            team_won = False
            
            if match.team1_id in registration_ids:
                team_registration_id = match.team1_id
                if match.team2:
                    opponent_name = match.team2.team.name
                team_won = match.winner_id == match.team1_id
            elif match.team2_id in registration_ids:
                team_registration_id = match.team2_id
                if match.team1:
                    opponent_name = match.team1.team.name
                team_won = match.winner_id == match.team2_id
            
            matches_data.append({
                'tournament_name': match.tournament.name,
                'opponent_name': opponent_name,
                'team_won': team_won,
                'is_draw': match.winner_id is None,
                'team_score': match.team1_score if match.team1_id == team_registration_id else match.team2_score,
                'opponent_score': match.team2_score if match.team1_id == team_registration_id else match.team1_score,
                'completed_at': match.completed_at
            })
        
        return matches_data
    
    @staticmethod
    def get_team_leaderboard_stats(team_ids: Optional[List[int]] = None) -> Dict[int, Dict[str, Any]]:
        """Get leaderboard statistics for teams."""
        # Base query for active teams
        query = Team.query.filter_by(is_active=True)
        
        if team_ids:
            query = query.filter(Team.id.in_(team_ids))
        
        teams = query.all()
        leaderboard_stats = {}
        
        for team in teams:
            stats = TeamService.get_team_statistics(team)
            leaderboard_stats[team.id] = {
                'name': team.name,
                'tag': team.tag,
                'total_matches': stats['total_matches'],
                'total_wins': stats['total_wins'],
                'win_rate': stats['win_rate'],
                'total_tournaments': stats['total_tournaments'],
                'completed_tournaments': stats['completed_tournaments'],
                'best_placement': min([p['placement'] for p in stats['tournament_placements']]) if stats['tournament_placements'] else None,
                'total_prize_money': sum([p['prize_won'] for p in stats['tournament_placements']]),
                'active_players': team.active_players_count
            }
        
        return leaderboard_stats
    
    @staticmethod
    def search_teams(search_term: str, limit: int = 10) -> List[Team]:
        """Search teams by name, tag, or description."""
        if not search_term:
            return []
        
        return Team.query.filter(
            Team.is_active == True,
            or_(
                Team.name.ilike(f'%{search_term}%'),
                Team.tag.ilike(f'%{search_term}%'),
                Team.description.ilike(f'%{search_term}%')
            )
        ).limit(limit).all()
    
    @staticmethod
    def get_team_members_with_stats(team: Team) -> List[Dict[str, Any]]:
        """Get team members with their individual statistics."""
        members = team.players.filter_by(is_active=True).all()
        members_data = []
        
        for member in members:
            member_data = {
                'id': member.id,
                'user_id': member.user_id,
                'in_game_name': member.in_game_name,
                'full_name': member.user.full_name,
                'avatar_url': member.user.avatar_url,
                'skill_level': member.skill_level,
                'matches_played': member.matches_played,
                'matches_won': member.matches_won,
                'win_rate': member.win_rate,
                'tournaments_participated': member.tournaments_participated,
                'tournaments_won': member.tournaments_won,
                'is_captain': member.id == team.captain_id,
                'joined_at': member.joined_at
            }
            members_data.append(member_data)
        
        # Sort by captain first, then by join date
        members_data.sort(key=lambda x: (not x['is_captain'], x['joined_at']))
        
        return members_data
