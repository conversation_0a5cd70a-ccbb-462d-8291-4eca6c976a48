"""
Bracket generation algorithms for different tournament formats.
"""
import math
from typing import List, Tuple, Optional, Dict, Any
from datetime import datetime, timedelta
from app.models import Tournament, TournamentTeam, Match, TournamentFormat, MatchStatus
from app import db


class BracketGenerator:
    """Service class for generating tournament brackets."""
    
    def __init__(self, tournament: Tournament):
        self.tournament = tournament
        self.teams = list(tournament.teams.filter_by(is_active=True).all())
        self.team_count = len(self.teams)
    
    def generate_bracket(self) -> List[Match]:
        """Generate bracket based on tournament format."""
        if self.tournament.format == TournamentFormat.SINGLE_ELIMINATION:
            return self._generate_single_elimination()
        elif self.tournament.format == TournamentFormat.DOUBLE_ELIMINATION:
            return self._generate_double_elimination()
        elif self.tournament.format == TournamentFormat.ROUND_ROBIN:
            return self._generate_round_robin()
        elif self.tournament.format == TournamentFormat.SWISS:
            return self._generate_swiss()
        else:
            raise ValueError(f"Unsupported tournament format: {self.tournament.format}")
    
    def _generate_single_elimination(self) -> List[Match]:
        """Generate single elimination bracket."""
        if self.team_count < 2:
            raise ValueError("Need at least 2 teams for single elimination")
        
        # Calculate number of rounds needed
        rounds_needed = math.ceil(math.log2(self.team_count))
        bracket_size = 2 ** rounds_needed
        
        # Seed teams (for now, use registration order)
        seeded_teams = self._seed_teams()
        
        matches = []
        current_teams = seeded_teams[:]
        
        # Add byes if needed
        while len(current_teams) < bracket_size:
            current_teams.append(None)  # Bye
        
        # Generate matches for each round
        for round_num in range(1, rounds_needed + 1):
            round_matches = []
            teams_this_round = current_teams[:]
            
            # Create matches for this round
            match_num = 1
            for i in range(0, len(teams_this_round), 2):
                team1 = teams_this_round[i] if i < len(teams_this_round) else None
                team2 = teams_this_round[i + 1] if i + 1 < len(teams_this_round) else None
                
                # Skip if both teams are None (shouldn't happen)
                if team1 is None and team2 is None:
                    continue
                
                # Handle byes
                if team1 is None:
                    current_teams = [team2 if team2 else None] + current_teams[2:]
                    continue
                elif team2 is None:
                    current_teams = [team1] + current_teams[2:]
                    continue
                
                match = Match(
                    tournament_id=self.tournament.id,
                    round_number=round_num,
                    match_number=match_num,
                    bracket_position=f"SE-R{round_num}-M{match_num}",
                    team1_id=team1.id if team1 else None,
                    team2_id=team2.id if team2 else None,
                    status=MatchStatus.SCHEDULED,
                    best_of=self._get_best_of_for_round(round_num, rounds_needed)
                )
                
                round_matches.append(match)
                match_num += 1
            
            matches.extend(round_matches)
            
            # Prepare for next round (winners advance)
            if round_num < rounds_needed:
                current_teams = [None] * len(round_matches)  # Placeholders for winners
        
        return matches
    
    def _generate_double_elimination(self) -> List[Match]:
        """Generate double elimination bracket."""
        if self.team_count < 2:
            raise ValueError("Need at least 2 teams for double elimination")
        
        matches = []
        seeded_teams = self._seed_teams()
        
        # Calculate bracket structure
        rounds_needed = math.ceil(math.log2(self.team_count))
        bracket_size = 2 ** rounds_needed
        
        # Winners bracket
        wb_matches = self._generate_winners_bracket(seeded_teams, bracket_size)
        matches.extend(wb_matches)
        
        # Losers bracket
        lb_matches = self._generate_losers_bracket(len(wb_matches))
        matches.extend(lb_matches)
        
        # Grand finals
        grand_final = Match(
            tournament_id=self.tournament.id,
            round_number=rounds_needed + len(lb_matches) // (self.team_count - 1) + 1,
            match_number=1,
            bracket_position="GF-M1",
            status=MatchStatus.SCHEDULED,
            best_of=self._get_best_of_for_round(-1, rounds_needed)  # Grand final
        )
        matches.append(grand_final)
        
        return matches
    
    def _generate_winners_bracket(self, teams: List[TournamentTeam], bracket_size: int) -> List[Match]:
        """Generate winners bracket for double elimination."""
        matches = []
        current_teams = teams[:]
        
        # Add byes if needed
        while len(current_teams) < bracket_size:
            current_teams.append(None)
        
        rounds_needed = math.ceil(math.log2(bracket_size))
        
        for round_num in range(1, rounds_needed + 1):
            match_num = 1
            for i in range(0, len(current_teams), 2):
                team1 = current_teams[i] if i < len(current_teams) else None
                team2 = current_teams[i + 1] if i + 1 < len(current_teams) else None
                
                if team1 is None and team2 is None:
                    continue
                
                if team1 is None or team2 is None:
                    continue  # Handle byes in double elim differently
                
                match = Match(
                    tournament_id=self.tournament.id,
                    round_number=round_num,
                    match_number=match_num,
                    bracket_position=f"WB-R{round_num}-M{match_num}",
                    team1_id=team1.id if team1 else None,
                    team2_id=team2.id if team2 else None,
                    status=MatchStatus.SCHEDULED,
                    best_of=self._get_best_of_for_round(round_num, rounds_needed)
                )
                
                matches.append(match)
                match_num += 1
            
            # Prepare for next round
            current_teams = [None] * (len(current_teams) // 2)
        
        return matches
    
    def _generate_losers_bracket(self, wb_match_count: int) -> List[Match]:
        """Generate losers bracket for double elimination."""
        matches = []
        
        # Simplified losers bracket generation
        # In a real implementation, this would be more complex
        losers_rounds = wb_match_count - 1
        
        for round_num in range(1, losers_rounds + 1):
            match = Match(
                tournament_id=self.tournament.id,
                round_number=round_num + 100,  # Offset to distinguish from WB
                match_number=1,
                bracket_position=f"LB-R{round_num}-M1",
                status=MatchStatus.SCHEDULED,
                best_of=1
            )
            matches.append(match)
        
        return matches
    
    def _generate_round_robin(self) -> List[Match]:
        """Generate round robin bracket."""
        if self.team_count < 2:
            raise ValueError("Need at least 2 teams for round robin")
        
        matches = []
        teams = self._seed_teams()
        
        match_num = 1
        for i in range(len(teams)):
            for j in range(i + 1, len(teams)):
                match = Match(
                    tournament_id=self.tournament.id,
                    round_number=1,  # All matches in round robin are "round 1"
                    match_number=match_num,
                    bracket_position=f"RR-M{match_num}",
                    team1_id=teams[i].id,
                    team2_id=teams[j].id,
                    status=MatchStatus.SCHEDULED,
                    best_of=self._get_best_of_for_round(1, 1)
                )
                matches.append(match)
                match_num += 1
        
        return matches
    
    def _generate_swiss(self) -> List[Match]:
        """Generate Swiss system bracket (first round only)."""
        if self.team_count < 4:
            raise ValueError("Need at least 4 teams for Swiss system")
        
        matches = []
        teams = self._seed_teams()
        
        # For Swiss, we only generate the first round initially
        # Subsequent rounds are generated based on results
        match_num = 1
        for i in range(0, len(teams), 2):
            if i + 1 < len(teams):
                match = Match(
                    tournament_id=self.tournament.id,
                    round_number=1,
                    match_number=match_num,
                    bracket_position=f"SW-R1-M{match_num}",
                    team1_id=teams[i].id,
                    team2_id=teams[i + 1].id,
                    status=MatchStatus.SCHEDULED,
                    best_of=self._get_best_of_for_round(1, 1)
                )
                matches.append(match)
                match_num += 1
        
        return matches
    
    def _seed_teams(self) -> List[TournamentTeam]:
        """Seed teams for bracket generation."""
        # For now, use registration order
        # In a real system, this could be based on rankings, random, or manual seeding
        teams = sorted(self.teams, key=lambda t: t.registered_at)
        
        # Set seed numbers
        for i, team in enumerate(teams):
            team.seed = i + 1
        
        return teams
    
    def _get_best_of_for_round(self, round_num: int, total_rounds: int) -> int:
        """Determine best-of format for a given round."""
        if round_num == -1:  # Grand final
            return 5
        elif round_num == total_rounds:  # Final
            return 3
        elif round_num >= total_rounds - 1:  # Semi-final
            return 3
        else:
            return 1  # Earlier rounds
    
    def schedule_matches(self, matches: List[Match], start_time: datetime, 
                        match_duration_minutes: int = 60, break_minutes: int = 15) -> None:
        """Schedule match times."""
        current_time = start_time
        
        # Group matches by round
        rounds = {}
        for match in matches:
            round_num = match.round_number
            if round_num not in rounds:
                rounds[round_num] = []
            rounds[round_num].append(match)
        
        # Schedule each round
        for round_num in sorted(rounds.keys()):
            round_matches = rounds[round_num]
            
            # Schedule matches in parallel for the same round
            for i, match in enumerate(round_matches):
                match.scheduled_time = current_time + timedelta(minutes=i * (match_duration_minutes + break_minutes))
            
            # Next round starts after all matches in current round are done
            if round_matches:
                last_match_end = max(m.scheduled_time for m in round_matches) + timedelta(minutes=match_duration_minutes)
                current_time = last_match_end + timedelta(minutes=break_minutes)


def generate_next_swiss_round(tournament: Tournament, round_number: int) -> List[Match]:
    """Generate next round for Swiss system based on current standings."""
    # Get current standings
    teams = list(tournament.teams.filter_by(is_active=True).all())
    
    # Calculate wins/losses for each team
    team_records = {}
    for team in teams:
        wins = 0
        losses = 0
        
        # Count wins and losses
        team_matches = Match.query.filter(
            Match.tournament_id == tournament.id,
            Match.status == MatchStatus.COMPLETED,
            (Match.team1_id == team.id) | (Match.team2_id == team.id)
        ).all()
        
        for match in team_matches:
            if match.winner_id == team.id:
                wins += 1
            else:
                losses += 1
        
        team_records[team.id] = {'team': team, 'wins': wins, 'losses': losses, 'score': wins}
    
    # Sort teams by score (wins)
    sorted_teams = sorted(team_records.values(), key=lambda x: x['score'], reverse=True)
    
    # Pair teams with similar scores
    matches = []
    used_teams = set()
    match_num = 1
    
    for i in range(0, len(sorted_teams), 2):
        if i + 1 < len(sorted_teams):
            team1 = sorted_teams[i]['team']
            team2 = sorted_teams[i + 1]['team']
            
            if team1.id not in used_teams and team2.id not in used_teams:
                match = Match(
                    tournament_id=tournament.id,
                    round_number=round_number,
                    match_number=match_num,
                    bracket_position=f"SW-R{round_number}-M{match_num}",
                    team1_id=team1.id,
                    team2_id=team2.id,
                    status=MatchStatus.SCHEDULED,
                    best_of=1
                )
                matches.append(match)
                used_teams.add(team1.id)
                used_teams.add(team2.id)
                match_num += 1
    
    return matches
