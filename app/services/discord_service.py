"""
Discord integration service for tournament notifications and bot commands.
"""
import asyncio
import logging
import os
from typing import Optional, Dict, Any, List
from datetime import datetime
import discord
from discord.ext import commands
import aiohttp
from app.models import Tournament, Match, TournamentStatus, MatchStatus
from app import db

logger = logging.getLogger(__name__)


class DiscordService:
    """Service for Discord integration including webhooks and bot commands."""
    
    def __init__(self, app=None):
        self.app = app
        self.bot = None
        self.webhook_url = None
        self.bot_token = None
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize Discord service with Flask app."""
        self.app = app
        self.webhook_url = app.config.get('DISCORD_WEBHOOK_URL')
        self.bot_token = app.config.get('DISCORD_BOT_TOKEN')
        
        if self.bot_token:
            self.setup_bot()
    
    def setup_bot(self):
        """Set up Discord bot with commands."""
        intents = discord.Intents.default()
        intents.message_content = True
        
        self.bot = commands.Bot(command_prefix='!', intents=intents)
        
        @self.bot.event
        async def on_ready():
            logger.info(f'{self.bot.user} has connected to Discord!')
        
        @self.bot.command(name='tournaments')
        async def list_tournaments(ctx):
            """List active tournaments."""
            try:
                with self.app.app_context():
                    tournaments = Tournament.query.filter(
                        Tournament.status.in_([
                            TournamentStatus.REGISTRATION_OPEN,
                            TournamentStatus.IN_PROGRESS
                        ])
                    ).limit(5).all()
                    
                    if not tournaments:
                        await ctx.send("No active tournaments found.")
                        return
                    
                    embed = discord.Embed(
                        title="🏆 Active Tournaments",
                        color=0x00ff00,
                        timestamp=datetime.utcnow()
                    )
                    
                    for tournament in tournaments:
                        status_emoji = "🔴" if tournament.status == TournamentStatus.IN_PROGRESS else "🟢"
                        embed.add_field(
                            name=f"{status_emoji} {tournament.name}",
                            value=f"Game: {tournament.game}\nStatus: {tournament.status.value}\nTeams: {len(tournament.teams.all())}",
                            inline=True
                        )
                    
                    await ctx.send(embed=embed)
            except Exception as e:
                logger.error(f"Error in tournaments command: {e}")
                await ctx.send("Error retrieving tournaments.")
        
        @self.bot.command(name='tournament')
        async def tournament_info(ctx, tournament_id: int):
            """Get detailed information about a specific tournament."""
            try:
                with self.app.app_context():
                    tournament = Tournament.query.get(tournament_id)
                    if not tournament:
                        await ctx.send("Tournament not found.")
                        return
                    
                    embed = discord.Embed(
                        title=f"🏆 {tournament.name}",
                        description=tournament.description or "No description available",
                        color=0x0099ff,
                        timestamp=datetime.utcnow()
                    )
                    
                    embed.add_field(name="Game", value=tournament.game, inline=True)
                    embed.add_field(name="Format", value=tournament.format.value, inline=True)
                    embed.add_field(name="Status", value=tournament.status.value, inline=True)
                    embed.add_field(name="Max Teams", value=tournament.max_teams, inline=True)
                    embed.add_field(name="Registered Teams", value=len(tournament.teams.all()), inline=True)
                    
                    if tournament.registration_start:
                        embed.add_field(
                            name="Registration Start",
                            value=tournament.registration_start.strftime("%Y-%m-%d %H:%M UTC"),
                            inline=True
                        )
                    
                    await ctx.send(embed=embed)
            except Exception as e:
                logger.error(f"Error in tournament command: {e}")
                await ctx.send("Error retrieving tournament information.")
        
        @self.bot.command(name='matches')
        async def upcoming_matches(ctx, tournament_id: int = None):
            """List upcoming matches for a tournament or all tournaments."""
            try:
                with self.app.app_context():
                    query = Match.query.filter_by(status=MatchStatus.SCHEDULED)
                    
                    if tournament_id:
                        query = query.filter_by(tournament_id=tournament_id)
                    
                    matches = query.order_by(Match.scheduled_time).limit(10).all()
                    
                    if not matches:
                        await ctx.send("No upcoming matches found.")
                        return
                    
                    embed = discord.Embed(
                        title="⚔️ Upcoming Matches",
                        color=0xff9900,
                        timestamp=datetime.utcnow()
                    )
                    
                    for match in matches:
                        team1_name = match.team1.name if match.team1 else "TBD"
                        team2_name = match.team2.name if match.team2 else "TBD"
                        
                        match_time = "TBD"
                        if match.scheduled_time:
                            match_time = match.scheduled_time.strftime("%Y-%m-%d %H:%M UTC")
                        
                        embed.add_field(
                            name=f"Round {match.round_number} - Match {match.match_number}",
                            value=f"{team1_name} vs {team2_name}\nTime: {match_time}\nTournament: {match.tournament.name}",
                            inline=False
                        )
                    
                    await ctx.send(embed=embed)
            except Exception as e:
                logger.error(f"Error in matches command: {e}")
                await ctx.send("Error retrieving matches.")
    
    async def send_webhook_notification(self, title: str, description: str, color: int = 0x00ff00, fields: List[Dict] = None):
        """Send notification via Discord webhook."""
        if not self.webhook_url:
            logger.warning("Discord webhook URL not configured")
            return False
        
        embed = {
            "title": title,
            "description": description,
            "color": color,
            "timestamp": datetime.utcnow().isoformat(),
            "footer": {
                "text": "Esports Tournament System"
            }
        }
        
        if fields:
            embed["fields"] = fields
        
        payload = {
            "embeds": [embed]
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.webhook_url, json=payload) as response:
                    if response.status == 204:
                        logger.info(f"Discord notification sent: {title}")
                        return True
                    else:
                        logger.error(f"Discord webhook failed with status {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error sending Discord notification: {e}")
            return False
    
    def notify_tournament_created(self, tournament: Tournament):
        """Send notification when a tournament is created."""
        if not self.webhook_url:
            return
        
        asyncio.create_task(self.send_webhook_notification(
            title="🏆 New Tournament Created!",
            description=f"**{tournament.name}** has been created and is now accepting registrations!",
            color=0x00ff00,
            fields=[
                {"name": "Game", "value": tournament.game, "inline": True},
                {"name": "Format", "value": tournament.format.value, "inline": True},
                {"name": "Max Teams", "value": str(tournament.max_teams), "inline": True},
                {"name": "Organizer", "value": tournament.organizer.username, "inline": True}
            ]
        ))
    
    def notify_tournament_started(self, tournament: Tournament):
        """Send notification when a tournament starts."""
        if not self.webhook_url:
            return
        
        asyncio.create_task(self.send_webhook_notification(
            title="🚀 Tournament Started!",
            description=f"**{tournament.name}** has officially begun! Good luck to all participants!",
            color=0xff9900,
            fields=[
                {"name": "Participating Teams", "value": str(len(tournament.teams.all())), "inline": True},
                {"name": "Format", "value": tournament.format.value, "inline": True}
            ]
        ))
    
    def notify_match_result(self, match: Match):
        """Send notification when a match result is submitted."""
        if not self.webhook_url or not match.team1_score or not match.team2_score:
            return
        
        winner = "Team 1" if match.team1_score > match.team2_score else "Team 2"
        winner_name = match.team1.name if match.team1_score > match.team2_score else match.team2.name
        
        asyncio.create_task(self.send_webhook_notification(
            title="⚔️ Match Result",
            description=f"**{match.team1.name}** vs **{match.team2.name}** - {winner_name} wins!",
            color=0x0099ff,
            fields=[
                {"name": "Score", "value": f"{match.team1_score} - {match.team2_score}", "inline": True},
                {"name": "Tournament", "value": match.tournament.name, "inline": True},
                {"name": "Round", "value": f"Round {match.round_number}", "inline": True}
            ]
        ))
    
    def run_bot(self):
        """Run the Discord bot (should be called in a separate thread)."""
        if self.bot and self.bot_token:
            try:
                asyncio.run(self.bot.run(self.bot_token))
            except Exception as e:
                logger.error(f"Error running Discord bot: {e}")


# Global Discord service instance
discord_service = DiscordService()
