/**
 * WebSocket client for real-time tournament and match updates
 */

class EsportsWebSocket {
    constructor() {
        this.socket = null;
        this.connected = false;
        this.currentTournament = null;
        this.currentMatch = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        
        this.init();
    }
    
    init() {
        // Initialize Socket.IO connection
        this.socket = io();
        this.setupEventListeners();
        this.setupReconnection();
    }
    
    setupEventListeners() {
        // Connection events
        this.socket.on('connect', () => {
            console.log('Connected to WebSocket server');
            this.connected = true;
            this.reconnectAttempts = 0;
            this.showNotification('Connected to live updates', 'success');
            
            // Rejoin rooms if needed
            if (this.currentTournament) {
                this.joinTournament(this.currentTournament);
            }
            if (this.currentMatch) {
                this.joinMatch(this.currentMatch);
            }
        });
        
        this.socket.on('disconnect', () => {
            console.log('Disconnected from WebSocket server');
            this.connected = false;
            this.showNotification('Disconnected from live updates', 'warning');
        });
        
        this.socket.on('connect_error', (error) => {
            console.error('WebSocket connection error:', error);
            this.showNotification('Connection error', 'error');
        });
        
        // Status and error messages
        this.socket.on('status', (data) => {
            console.log('Status:', data.msg);
        });
        
        this.socket.on('error', (data) => {
            console.error('WebSocket error:', data.msg);
            this.showNotification(data.msg, 'error');
        });
        
        // Tournament events
        this.socket.on('tournament_status', (data) => {
            this.updateTournamentStatus(data);
        });
        
        this.socket.on('tournament_notification', (data) => {
            this.handleTournamentNotification(data);
        });
        
        this.socket.on('tournament_started', (data) => {
            this.showNotification(`🚀 ${data.name} has started!`, 'info');
            this.updateTournamentStatus({
                tournament_id: data.tournament_id,
                status: 'in_progress'
            });
        });
        
        this.socket.on('tournament_match_completed', (data) => {
            this.showNotification(`Match completed: ${data.result}`, 'success');
            this.updateTournamentMatches();
        });
        
        this.socket.on('new_match_scheduled', (data) => {
            this.showNotification(`New match scheduled: ${data.team1} vs ${data.team2}`, 'info');
            this.updateTournamentMatches();
        });
        
        // Match events
        this.socket.on('match_status', (data) => {
            this.updateMatchStatus(data);
        });
        
        this.socket.on('live_score_update', (data) => {
            this.updateLiveScore(data);
        });
        
        this.socket.on('match_completed', (data) => {
            this.handleMatchCompleted(data);
        });
        
        this.socket.on('match_notification', (data) => {
            this.handleMatchNotification(data);
        });
    }
    
    setupReconnection() {
        this.socket.on('disconnect', () => {
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                setTimeout(() => {
                    console.log(`Attempting to reconnect... (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);
                    this.reconnectAttempts++;
                    this.socket.connect();
                }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
            }
        });
    }
    
    // Tournament methods
    joinTournament(tournamentId) {
        if (!this.connected) return;
        
        this.currentTournament = tournamentId;
        this.socket.emit('join_tournament', { tournament_id: tournamentId });
        console.log(`Joined tournament ${tournamentId} for live updates`);
    }
    
    leaveTournament() {
        if (!this.connected || !this.currentTournament) return;
        
        this.socket.emit('leave_tournament', { tournament_id: this.currentTournament });
        this.currentTournament = null;
        console.log('Left tournament updates');
    }
    
    // Match methods
    joinMatch(matchId) {
        if (!this.connected) return;
        
        this.currentMatch = matchId;
        this.socket.emit('join_match', { match_id: matchId });
        console.log(`Joined match ${matchId} for live updates`);
    }
    
    leaveMatch() {
        if (!this.connected || !this.currentMatch) return;
        
        this.socket.emit('leave_match', { match_id: this.currentMatch });
        this.currentMatch = null;
        console.log('Left match updates');
    }
    
    updateLiveScore(matchId, team1Score, team2Score) {
        if (!this.connected) return;
        
        this.socket.emit('update_live_score', {
            match_id: matchId,
            team1_score: team1Score,
            team2_score: team2Score,
            timestamp: new Date().toISOString()
        });
    }
    
    requestTournamentUpdate(tournamentId) {
        if (!this.connected) return;
        
        this.socket.emit('request_tournament_update', { tournament_id: tournamentId });
    }
    
    // Event handlers
    updateTournamentStatus(data) {
        const statusElement = document.getElementById('tournament-status');
        if (statusElement) {
            statusElement.textContent = data.status;
            statusElement.className = `badge badge-${this.getStatusClass(data.status)}`;
        }
        
        const teamsElement = document.getElementById('registered-teams-count');
        if (teamsElement && data.registered_teams !== undefined) {
            teamsElement.textContent = data.registered_teams;
        }
    }
    
    updateMatchStatus(data) {
        const matchElement = document.getElementById(`match-${data.match_id}`);
        if (matchElement) {
            const team1ScoreEl = matchElement.querySelector('.team1-score');
            const team2ScoreEl = matchElement.querySelector('.team2-score');
            const statusEl = matchElement.querySelector('.match-status');
            
            if (team1ScoreEl) team1ScoreEl.textContent = data.team1_score;
            if (team2ScoreEl) team2ScoreEl.textContent = data.team2_score;
            if (statusEl) {
                statusEl.textContent = data.status;
                statusEl.className = `badge badge-${this.getStatusClass(data.status)}`;
            }
        }
    }
    
    updateLiveScore(data) {
        const matchElement = document.getElementById(`match-${data.match_id}`);
        if (matchElement) {
            const team1ScoreEl = matchElement.querySelector('.team1-score');
            const team2ScoreEl = matchElement.querySelector('.team2-score');
            
            if (team1ScoreEl) {
                team1ScoreEl.textContent = data.team1_score;
                team1ScoreEl.classList.add('score-updated');
                setTimeout(() => team1ScoreEl.classList.remove('score-updated'), 2000);
            }
            
            if (team2ScoreEl) {
                team2ScoreEl.textContent = data.team2_score;
                team2ScoreEl.classList.add('score-updated');
                setTimeout(() => team2ScoreEl.classList.remove('score-updated'), 2000);
            }
        }
        
        this.showNotification(
            `Live update: ${data.team1} ${data.team1_score} - ${data.team2_score} ${data.team2}`,
            'info'
        );
    }
    
    handleMatchCompleted(data) {
        const matchElement = document.getElementById(`match-${data.match_id}`);
        if (matchElement) {
            matchElement.classList.add('match-completed');
            
            const statusEl = matchElement.querySelector('.match-status');
            if (statusEl) {
                statusEl.textContent = 'Completed';
                statusEl.className = 'badge badge-success';
            }
            
            const winnerEl = matchElement.querySelector('.match-winner');
            if (winnerEl) {
                winnerEl.textContent = `Winner: ${data.winner}`;
                winnerEl.style.display = 'block';
            }
        }
        
        this.showNotification(
            `Match completed: ${data.team1} ${data.team1_score} - ${data.team2_score} ${data.team2}`,
            'success'
        );
    }
    
    handleTournamentNotification(data) {
        console.log('Tournament notification:', data);
        // Handle different types of tournament notifications
        switch (data.type) {
            case 'team_registered':
                this.updateTournamentTeamCount();
                break;
            case 'bracket_generated':
                this.refreshBracket();
                break;
            case 'status_changed':
                this.updateTournamentStatus(data.data);
                break;
        }
    }
    
    handleMatchNotification(data) {
        console.log('Match notification:', data);
        // Handle different types of match notifications
    }
    
    // Utility methods
    updateTournamentMatches() {
        // Refresh the matches section if it exists
        const matchesContainer = document.getElementById('tournament-matches');
        if (matchesContainer && this.currentTournament) {
            // You could implement an AJAX call to refresh matches here
            this.requestTournamentUpdate(this.currentTournament);
        }
    }
    
    updateTournamentTeamCount() {
        const teamsCountEl = document.getElementById('registered-teams-count');
        if (teamsCountEl && this.currentTournament) {
            this.requestTournamentUpdate(this.currentTournament);
        }
    }
    
    refreshBracket() {
        const bracketContainer = document.getElementById('tournament-bracket');
        if (bracketContainer) {
            // Trigger bracket refresh
            window.location.reload(); // Simple approach, could be improved with AJAX
        }
    }
    
    getStatusClass(status) {
        const statusClasses = {
            'draft': 'secondary',
            'registration_open': 'primary',
            'registration_closed': 'warning',
            'in_progress': 'info',
            'completed': 'success',
            'cancelled': 'danger',
            'scheduled': 'primary',
            'live': 'warning'
        };
        return statusClasses[status] || 'secondary';
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show websocket-notification`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Add to notifications container or body
        const container = document.getElementById('notifications-container') || document.body;
        container.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Initialize WebSocket when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.esportsWS = new EsportsWebSocket();
    
    // Auto-join tournament room if on tournament page
    const tournamentId = document.querySelector('[data-tournament-id]')?.dataset.tournamentId;
    if (tournamentId) {
        window.esportsWS.joinTournament(parseInt(tournamentId));
    }
    
    // Auto-join match room if on match page
    const matchId = document.querySelector('[data-match-id]')?.dataset.matchId;
    if (matchId) {
        window.esportsWS.joinMatch(parseInt(matchId));
    }
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (window.esportsWS) {
        window.esportsWS.leaveTournament();
        window.esportsWS.leaveMatch();
    }
});
