// Esports Tournament Manager - Enhanced JavaScript

// Global App Object
const EsportsApp = {
    init() {
        this.initAnimations();
        this.initInteractiveElements();
        this.initFormValidation();
        this.initNotifications();
        this.initLiveUpdates();
        this.initTooltips();
        this.initModals();
        console.log('Esports Tournament Manager initialized');
    },

    // Initialize animations and transitions
    initAnimations() {
        // Add fade-in animation to cards on page load
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // Add hover effects to buttons
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    },

    // Initialize interactive elements
    initInteractiveElements() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.style.transition = 'opacity 0.5s ease';
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        if (alert.parentNode) {
                            alert.remove();
                        }
                    }, 500);
                }
            }, 5000);
        });

        // Add loading states to forms
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="loading-spinner"></span> Processing...';
                    submitBtn.disabled = true;
                    
                    // Re-enable after 10 seconds as fallback
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 10000);
                }
            });
        });

        // Add search functionality
        this.initSearch();
    },

    // Initialize search functionality
    initSearch() {
        const searchInputs = document.querySelectorAll('[data-search]');
        searchInputs.forEach(input => {
            const targetSelector = input.getAttribute('data-search');
            const targets = document.querySelectorAll(targetSelector);
            
            input.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                targets.forEach(target => {
                    const text = target.textContent.toLowerCase();
                    if (text.includes(query)) {
                        target.style.display = '';
                        target.classList.add('fade-in');
                    } else {
                        target.style.display = 'none';
                        target.classList.remove('fade-in');
                    }
                });
            });
        });
    },

    // Initialize form validation
    initFormValidation() {
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // Add shake animation to invalid form
                    form.style.animation = 'shake 0.5s ease-in-out';
                    setTimeout(() => {
                        form.style.animation = '';
                    }, 500);
                }
                form.classList.add('was-validated');
            });
        });

        // Real-time validation feedback
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        });
    },

    // Initialize notifications
    initNotifications() {
        // Check for browser notification support
        if ('Notification' in window) {
            if (Notification.permission === 'default') {
                Notification.requestPermission();
            }
        }
    },

    // Show notification
    showNotification(title, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                icon: '/static/img/logo.png',
                badge: '/static/img/badge.png',
                ...options
            });
        }
    },

    // Initialize live updates
    initLiveUpdates() {
        // Auto-refresh live matches every 30 seconds
        if (document.querySelector('[data-live-refresh]')) {
            setInterval(() => {
                this.refreshLiveContent();
            }, 30000);
        }

        // WebSocket connection for real-time updates (if available)
        this.initWebSocket();
    },

    // Refresh live content
    refreshLiveContent() {
        const liveElements = document.querySelectorAll('[data-live-refresh]');
        liveElements.forEach(element => {
            const url = element.getAttribute('data-live-refresh');
            if (url) {
                fetch(url)
                    .then(response => response.text())
                    .then(html => {
                        element.innerHTML = html;
                        element.classList.add('fade-in');
                    })
                    .catch(error => {
                        console.error('Error refreshing live content:', error);
                    });
            }
        });
    },

    // Initialize WebSocket for real-time updates
    initWebSocket() {
        if (typeof io !== 'undefined') {
            const socket = io();
            
            socket.on('match_update', (data) => {
                this.handleMatchUpdate(data);
            });
            
            socket.on('tournament_update', (data) => {
                this.handleTournamentUpdate(data);
            });
            
            socket.on('notification', (data) => {
                this.showNotification(data.title, {
                    body: data.message,
                    tag: data.type
                });
            });
        }
    },

    // Handle match updates
    handleMatchUpdate(data) {
        const matchElement = document.querySelector(`[data-match-id="${data.match_id}"]`);
        if (matchElement) {
            // Update match status and scores
            const statusElement = matchElement.querySelector('.match-status');
            const scoreElement = matchElement.querySelector('.match-score');
            
            if (statusElement) {
                statusElement.textContent = data.status;
                statusElement.className = `match-status badge bg-${this.getStatusColor(data.status)}`;
            }
            
            if (scoreElement && data.score) {
                scoreElement.textContent = `${data.score.team1} - ${data.score.team2}`;
            }
            
            // Add update animation
            matchElement.classList.add('pulse');
            setTimeout(() => {
                matchElement.classList.remove('pulse');
            }, 2000);
        }
    },

    // Handle tournament updates
    handleTournamentUpdate(data) {
        const tournamentElement = document.querySelector(`[data-tournament-id="${data.tournament_id}"]`);
        if (tournamentElement) {
            // Update tournament status
            const statusElement = tournamentElement.querySelector('.tournament-status');
            if (statusElement) {
                statusElement.textContent = data.status;
                statusElement.className = `tournament-status badge bg-${this.getStatusColor(data.status)}`;
            }
            
            // Add update animation
            tournamentElement.classList.add('pulse');
            setTimeout(() => {
                tournamentElement.classList.remove('pulse');
            }, 2000);
        }
    },

    // Get status color for badges
    getStatusColor(status) {
        const statusColors = {
            'completed': 'success',
            'in_progress': 'warning',
            'upcoming': 'info',
            'cancelled': 'danger',
            'registration_open': 'success',
            'registration_closed': 'secondary'
        };
        return statusColors[status] || 'secondary';
    },

    // Initialize tooltips
    initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },

    // Initialize modals
    initModals() {
        // Auto-focus first input in modals
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.addEventListener('shown.bs.modal', function() {
                const firstInput = this.querySelector('input, textarea, select');
                if (firstInput) {
                    firstInput.focus();
                }
            });
        });
    },

    // Utility function to show toast notifications
    showToast(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
    },

    // Create toast container if it doesn't exist
    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
        return container;
    }
};

// Bracket Visualization Module
const BracketVisualizer = {
    init(containerId, tournamentData) {
        this.container = document.getElementById(containerId);
        this.data = tournamentData;
        this.render();
    },

    render() {
        if (!this.container || !this.data) return;
        
        // Clear container
        this.container.innerHTML = '';
        
        // Create bracket structure based on tournament format
        if (this.data.format === 'single_elimination') {
            this.renderSingleElimination();
        } else if (this.data.format === 'double_elimination') {
            this.renderDoubleElimination();
        } else {
            this.renderRoundRobin();
        }
    },

    renderSingleElimination() {
        const rounds = this.data.rounds || [];
        const bracketDiv = document.createElement('div');
        bracketDiv.className = 'bracket-container d-flex justify-content-start overflow-auto p-3';
        bracketDiv.style.minHeight = '400px';

        rounds.forEach((round, roundIndex) => {
            const roundDiv = document.createElement('div');
            roundDiv.className = 'bracket-round me-4';
            roundDiv.innerHTML = `<h6 class="text-center mb-3 text-primary">${round.name}</h6>`;

            round.matches.forEach((match, matchIndex) => {
                const matchDiv = document.createElement('div');
                const statusClass = this.getMatchStatusClass(match.status);
                matchDiv.className = `bracket-match mb-3 ${statusClass}`;
                matchDiv.setAttribute('data-match-id', match.id);

                // Add click handler for match details
                matchDiv.addEventListener('click', () => this.showMatchDetails(match));

                const team1Score = match.team1_score || '';
                const team2Score = match.team2_score || '';
                const showScores = match.status === 'completed' && (team1Score || team2Score);

                matchDiv.innerHTML = `
                    <div class="team ${match.winner_id === match.team1_id ? 'winner' : ''}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="team-name">${match.team1_name || 'TBD'}</span>
                            ${showScores ? `<span class="team-score badge bg-primary">${team1Score}</span>` : ''}
                        </div>
                    </div>
                    <div class="team ${match.winner_id === match.team2_id ? 'winner' : ''}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="team-name">${match.team2_name || 'TBD'}</span>
                            ${showScores ? `<span class="team-score badge bg-primary">${team2Score}</span>` : ''}
                        </div>
                    </div>
                    <div class="match-info text-center mt-2">
                        <small class="text-muted">
                            ${match.scheduled_time ? new Date(match.scheduled_time).toLocaleDateString() : 'TBD'}
                        </small>
                    </div>
                `;

                // Add connecting lines for visual flow
                if (roundIndex < rounds.length - 1) {
                    matchDiv.style.position = 'relative';
                    const connector = document.createElement('div');
                    connector.className = 'bracket-connector';
                    matchDiv.appendChild(connector);
                }

                roundDiv.appendChild(matchDiv);
            });

            bracketDiv.appendChild(roundDiv);
        });

        this.container.appendChild(bracketDiv);

        // Add bracket navigation if it's large
        if (rounds.length > 3) {
            this.addBracketNavigation();
        }
    },

    getMatchStatusClass(status) {
        const statusClasses = {
            'completed': 'completed',
            'in_progress': 'in-progress',
            'upcoming': 'upcoming',
            'cancelled': 'cancelled'
        };
        return statusClasses[status] || 'upcoming';
    },

    showMatchDetails(match) {
        // Create modal for match details
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Match Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-6 text-center">
                                <h6>${match.team1_name || 'TBD'}</h6>
                                ${match.team1_score ? `<div class="h4 text-primary">${match.team1_score}</div>` : ''}
                            </div>
                            <div class="col-6 text-center">
                                <h6>${match.team2_name || 'TBD'}</h6>
                                ${match.team2_score ? `<div class="h4 text-primary">${match.team2_score}</div>` : ''}
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <p><strong>Status:</strong> <span class="badge bg-${this.getStatusColor(match.status)}">${match.status}</span></p>
                            ${match.scheduled_time ? `<p><strong>Scheduled:</strong> ${new Date(match.scheduled_time).toLocaleString()}</p>` : ''}
                            ${match.winner_id ? `<p><strong>Winner:</strong> ${match.winner_id === match.team1_id ? match.team1_name : match.team2_name}</p>` : ''}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        ${match.status === 'upcoming' ? `<a href="/matches/${match.id}/edit" class="btn btn-primary">Edit Match</a>` : ''}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    },

    addBracketNavigation() {
        const navDiv = document.createElement('div');
        navDiv.className = 'bracket-navigation d-flex justify-content-center mt-3';
        navDiv.innerHTML = `
            <button class="btn btn-outline-primary btn-sm me-2" onclick="this.scrollBracket('left')">
                <i class="fas fa-chevron-left"></i> Previous
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="this.scrollBracket('right')">
                Next <i class="fas fa-chevron-right"></i>
            </button>
        `;
        this.container.appendChild(navDiv);
    },

    scrollBracket(direction) {
        const container = this.container.querySelector('.bracket-container');
        const scrollAmount = 300;

        if (direction === 'left') {
            container.scrollLeft -= scrollAmount;
        } else {
            container.scrollLeft += scrollAmount;
        }
    },

    renderDoubleElimination() {
        // Simplified double elimination bracket
        this.container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Double elimination bracket visualization is being enhanced. 
                Please check the tournament schedule for match details.
            </div>
        `;
    },

    renderRoundRobin() {
        // Round robin doesn't need bracket visualization
        this.container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Round robin format uses a league table instead of brackets. 
                Check the leaderboard for current standings.
            </div>
        `;
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    EsportsApp.init();
});

// Export for global access
window.EsportsApp = EsportsApp;
window.BracketVisualizer = BracketVisualizer;
