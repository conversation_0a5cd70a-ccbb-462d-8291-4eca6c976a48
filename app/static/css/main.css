/* Esports Tournament Manager - Enhanced CSS */

/* Custom CSS Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 0.5rem;
    --transition: all 0.3s ease;
}

/* Enhanced Body and Layout */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Enhanced Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-2px);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    overflow: hidden;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border-bottom: none;
    font-weight: 600;
}

/* Tournament Cards */
.tournament-card {
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.tournament-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.tournament-card:hover::before {
    left: 100%;
}

.tournament-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Status Badges */
.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
}

.badge.bg-success {
    background: var(--gradient-success) !important;
}

.badge.bg-warning {
    background: var(--gradient-warning) !important;
}

/* Enhanced Buttons */
.btn {
    border-radius: 2rem;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: var(--transition);
    transform: translate(-50%, -50%);
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--gradient-success);
    border: none;
}

.btn-warning {
    background: var(--gradient-warning);
    border: none;
    color: white;
}

/* Hero Section */
.jumbotron {
    background: var(--gradient-primary) !important;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.jumbotron::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* Statistics Cards */
.stats-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stats-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Form Enhancements */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-2px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Loading Animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Slide In Animation */
.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Enhanced Footer */
.footer {
    background: var(--gradient-primary);
    color: white;
    margin-top: 4rem;
    padding: 3rem 0 2rem;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

/* Responsive Design */
@media (max-width: 768px) {
    .tournament-card {
        margin-bottom: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
    
    .stats-card {
        padding: 1.5rem;
    }
    
    .stats-card i {
        font-size: 2rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        color: #e9ecef;
    }
    
    .card {
        background: rgba(33, 37, 41, 0.95);
        color: #e9ecef;
    }
    
    .form-control {
        background: rgba(33, 37, 41, 0.8);
        border-color: #495057;
        color: #e9ecef;
    }
}

/* WebSocket Live Updates */
.websocket-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1050;
    max-width: 350px;
    animation: slideInRight 0.3s ease-out;
}

.score-updated {
    animation: scoreFlash 0.5s ease-in-out;
    background-color: var(--primary-color) !important;
    color: white !important;
    border-radius: 4px;
    padding: 2px 6px;
}

.match-completed {
    border: 2px solid var(--success-color);
    background-color: rgba(40, 167, 69, 0.1);
}

.live-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #ff4444;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
    margin-right: 5px;
}

/* Stream styling */
.stream-container {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    background: var(--card-bg);
}

.stream-container:last-child {
    margin-bottom: 0 !important;
}

.bg-purple {
    background-color: #9146ff !important;
}

/* Platform icons */
.fab.fa-twitch {
    color: #9146ff;
}

.fab.fa-youtube {
    color: #ff0000;
}

/* Stream embed responsive */
.ratio-16x9 {
    --bs-aspect-ratio: 56.25%;
}

.ratio-16x9 iframe {
    border-radius: 8px;
}

/* Stream validation alerts */
.alert.py-2 {
    padding: 0.5rem 0.75rem;
}

/* Stream management buttons */
.stream-container .dropdown-menu {
    min-width: 120px;
}

.stream-container .dropdown-item {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes scoreFlash {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Connection Status */
.connection-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 1000;
}

.connection-status.connected {
    background-color: var(--success-color);
    color: white;
}

.connection-status.disconnected {
    background-color: var(--danger-color);
    color: white;
}
