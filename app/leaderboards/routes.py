"""Leaderboard routes."""
from flask import render_template, request, jsonify, abort
from app.leaderboards import bp
from app.models import Tournament, TournamentStatus
from app.services.leaderboard_service import LeaderboardService


@bp.route('/')
def index():
    """List all tournament leaderboards."""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status')
    
    query = Tournament.query.filter_by(is_active=True)
    
    # Filter by status if specified
    if status and status in [s.value for s in TournamentStatus]:
        query = query.filter_by(status=TournamentStatus(status))
    
    # Only show tournaments that have started or completed
    query = query.filter(Tournament.status.in_([
        TournamentStatus.IN_PROGRESS,
        TournamentStatus.COMPLETED
    ]))
    
    tournaments = query.order_by(Tournament.created_at.desc()).paginate(
        page=page, per_page=12, error_out=False
    )
    
    return render_template('leaderboards/index.html',
                         title='Tournament Leaderboards',
                         tournaments=tournaments,
                         current_status=status)


@bp.route('/tournament/<int:tournament_id>')
def tournament_leaderboard(tournament_id):
    """View leaderboard for a specific tournament."""
    tournament = Tournament.query.get_or_404(tournament_id)
    
    # Check if tournament has started
    if tournament.status not in [TournamentStatus.IN_PROGRESS, TournamentStatus.COMPLETED]:
        abort(404, description="Leaderboard not available for this tournament")
    
    leaderboard_service = LeaderboardService(tournament)
    leaderboard = leaderboard_service.get_tournament_leaderboard()
    statistics = leaderboard_service.get_tournament_statistics()
    
    return render_template('leaderboards/tournament.html',
                         title=f'{tournament.name} - Leaderboard',
                         tournament=tournament,
                         leaderboard=leaderboard,
                         statistics=statistics)


@bp.route('/tournament/<int:tournament_id>/api')
def tournament_leaderboard_api(tournament_id):
    """API endpoint for tournament leaderboard data."""
    tournament = Tournament.query.get_or_404(tournament_id)
    
    if tournament.status not in [TournamentStatus.IN_PROGRESS, TournamentStatus.COMPLETED]:
        return jsonify({'error': 'Leaderboard not available'}), 404
    
    leaderboard_service = LeaderboardService(tournament)
    leaderboard = leaderboard_service.get_tournament_leaderboard()
    statistics = leaderboard_service.get_tournament_statistics()
    
    return jsonify({
        'tournament': {
            'id': tournament.id,
            'name': tournament.name,
            'format': tournament.format.value,
            'status': tournament.status.value
        },
        'leaderboard': leaderboard,
        'statistics': statistics,
        'last_updated': tournament.updated_at.isoformat() if tournament.updated_at else None
    })


@bp.route('/live')
def live_leaderboards():
    """View live leaderboards for all active tournaments."""
    # Get all tournaments currently in progress
    tournaments = Tournament.query.filter_by(
        status=TournamentStatus.IN_PROGRESS,
        is_active=True
    ).order_by(Tournament.tournament_start.desc()).all()
    
    live_data = []
    for tournament in tournaments:
        leaderboard_service = LeaderboardService(tournament)
        leaderboard = leaderboard_service.get_tournament_leaderboard()
        statistics = leaderboard_service.get_tournament_statistics()
        
        # Get top 3 teams
        top_teams = leaderboard[:3] if leaderboard else []
        
        live_data.append({
            'tournament': tournament,
            'top_teams': top_teams,
            'statistics': statistics
        })
    
    return render_template('leaderboards/live.html',
                         title='Live Tournament Leaderboards',
                         live_data=live_data)


@bp.route('/global')
def global_leaderboard():
    """View global leaderboard across all tournaments."""
    from app.models import Team, TournamentTeam, Tournament
    from app import db

    # Get teams with tournament participation
    teams_data = db.session.query(
        Team.id,
        Team.name,
        Team.tag,
        db.func.count(TournamentTeam.id).label('tournaments_played')
    ).join(TournamentTeam).join(Tournament).filter(
        Tournament.is_active == True
    ).group_by(Team.id, Team.name, Team.tag).order_by(
        db.func.count(TournamentTeam.id).desc()
    ).limit(50).all()

    # Calculate additional stats for each team
    global_leaderboard = []
    for i, team_data in enumerate(teams_data):
        # Get first places
        first_places = db.session.query(TournamentTeam).filter(
            TournamentTeam.team_id == team_data.id,
            TournamentTeam.placement == 1
        ).count()

        # Get podium finishes (top 3)
        podium_finishes = db.session.query(TournamentTeam).filter(
            TournamentTeam.team_id == team_data.id,
            TournamentTeam.placement <= 3,
            TournamentTeam.placement.isnot(None)
        ).count()

        global_leaderboard.append({
            'rank': i + 1,
            'team_id': team_data.id,
            'team_name': team_data.name,
            'team_tag': team_data.tag,
            'tournaments_played': team_data.tournaments_played,
            'first_places': first_places,
            'podium_finishes': podium_finishes,
            'total_matches': 0,  # Simplified for now
            'total_wins': 0,     # Simplified for now
            'win_percentage': 0  # Simplified for now
        })

    return render_template('leaderboards/global.html',
                         title='Global Team Rankings',
                         global_leaderboard=global_leaderboard)
