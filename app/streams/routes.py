"""Stream management routes."""
from flask import render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from app.streams import bp
from app.models import Tournament, Match, Stream, UserRole
from app.services.stream_service import stream_service
from app import db


@bp.route('/tournament/<int:tournament_id>/add', methods=['GET', 'POST'])
@login_required
def add_tournament_stream(tournament_id):
    """Add a stream to a tournament."""
    tournament = Tournament.query.get_or_404(tournament_id)
    
    # Check permissions
    if current_user.role not in [UserRole.ADMIN, UserRole.ORGANIZER]:
        flash('You do not have permission to manage streams.', 'error')
        return redirect(url_for('tournaments.view', id=tournament_id))
    
    if current_user.role == UserRole.ORGANIZER and tournament.organizer_id != current_user.id:
        flash('You can only manage streams for your own tournaments.', 'error')
        return redirect(url_for('tournaments.view', id=tournament_id))
    
    if request.method == 'POST':
        stream_url = request.form.get('stream_url', '').strip()
        title = request.form.get('title', '').strip()
        description = request.form.get('description', '').strip()
        
        if not stream_url:
            flash('Stream URL is required.', 'error')
            return render_template('streams/add_tournament_stream.html', tournament=tournament)
        
        # Create stream
        stream = stream_service.create_tournament_stream(
            tournament_id=tournament_id,
            stream_url=stream_url,
            title=title or None,
            description=description or None
        )
        
        if stream:
            flash(f'Stream added successfully for {tournament.name}!', 'success')
            return redirect(url_for('tournaments.view', id=tournament_id))
        else:
            flash('Failed to add stream. Please check the URL and try again.', 'error')
    
    return render_template('streams/add_tournament_stream.html', tournament=tournament)


@bp.route('/match/<int:match_id>/add', methods=['GET', 'POST'])
@login_required
def add_match_stream(match_id):
    """Add a stream to a specific match."""
    match = Match.query.get_or_404(match_id)
    
    # Check permissions
    if current_user.role not in [UserRole.ADMIN, UserRole.ORGANIZER]:
        flash('You do not have permission to manage streams.', 'error')
        return redirect(url_for('matches.view', id=match_id))
    
    if current_user.role == UserRole.ORGANIZER and match.tournament.organizer_id != current_user.id:
        flash('You can only manage streams for matches in your tournaments.', 'error')
        return redirect(url_for('matches.view', id=match_id))
    
    if request.method == 'POST':
        stream_url = request.form.get('stream_url', '').strip()
        title = request.form.get('title', '').strip()
        description = request.form.get('description', '').strip()
        
        if not stream_url:
            flash('Stream URL is required.', 'error')
            return render_template('streams/add_match_stream.html', match=match)
        
        # Create stream
        stream = stream_service.create_match_stream(
            match_id=match_id,
            stream_url=stream_url,
            title=title or None,
            description=description or None
        )
        
        if stream:
            flash(f'Stream added successfully for {match.teams_display}!', 'success')
            return redirect(url_for('matches.view', id=match_id))
        else:
            flash('Failed to add stream. Please check the URL and try again.', 'error')
    
    return render_template('streams/add_match_stream.html', match=match)


@bp.route('/<int:stream_id>/deactivate', methods=['POST'])
@login_required
def deactivate_stream(stream_id):
    """Deactivate a stream."""
    stream = Stream.query.get_or_404(stream_id)
    
    # Check permissions
    if current_user.role not in [UserRole.ADMIN, UserRole.ORGANIZER]:
        flash('You do not have permission to manage streams.', 'error')
        return redirect(url_for('tournaments.view', id=stream.tournament_id))
    
    if current_user.role == UserRole.ORGANIZER and stream.tournament.organizer_id != current_user.id:
        flash('You can only manage streams for your own tournaments.', 'error')
        return redirect(url_for('tournaments.view', id=stream.tournament_id))
    
    if stream_service.deactivate_stream(stream_id):
        flash('Stream deactivated successfully.', 'success')
    else:
        flash('Failed to deactivate stream.', 'error')
    
    # Redirect back to appropriate page
    if stream.match_id:
        return redirect(url_for('matches.view', id=stream.match_id))
    else:
        return redirect(url_for('tournaments.view', id=stream.tournament_id))


@bp.route('/<int:stream_id>/delete', methods=['POST'])
@login_required
def delete_stream(stream_id):
    """Delete a stream."""
    stream = Stream.query.get_or_404(stream_id)
    
    # Check permissions
    if current_user.role not in [UserRole.ADMIN, UserRole.ORGANIZER]:
        flash('You do not have permission to manage streams.', 'error')
        return redirect(url_for('tournaments.view', id=stream.tournament_id))
    
    if current_user.role == UserRole.ORGANIZER and stream.tournament.organizer_id != current_user.id:
        flash('You can only manage streams for your own tournaments.', 'error')
        return redirect(url_for('tournaments.view', id=stream.tournament_id))
    
    # Store redirect info before deletion
    tournament_id = stream.tournament_id
    match_id = stream.match_id
    
    if stream_service.delete_stream(stream_id):
        flash('Stream deleted successfully.', 'success')
    else:
        flash('Failed to delete stream.', 'error')
    
    # Redirect back to appropriate page
    if match_id:
        return redirect(url_for('matches.view', id=match_id))
    else:
        return redirect(url_for('tournaments.view', id=tournament_id))


@bp.route('/api/validate', methods=['POST'])
@login_required
def validate_stream_url():
    """API endpoint to validate stream URL."""
    if current_user.role not in [UserRole.ADMIN, UserRole.ORGANIZER]:
        return jsonify({'error': 'Insufficient permissions'}), 403
    
    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({'error': 'URL is required'}), 400
    
    stream_url = data['url'].strip()
    if not stream_url:
        return jsonify({'error': 'URL cannot be empty'}), 400
    
    # Parse the URL
    stream_info = stream_service.parse_stream_url(stream_url)
    
    if not stream_info['platform'] or stream_info['platform'] == 'unknown':
        return jsonify({
            'valid': False,
            'error': 'Unsupported platform. Please use Twitch or YouTube URLs.'
        })
    
    # Validate with platform APIs if available
    is_valid = True
    error_message = None
    
    try:
        if stream_info['platform'] == 'twitch':
            is_valid = stream_service.validate_twitch_channel(stream_info['stream_id'])
            if not is_valid:
                error_message = 'Twitch channel not found'
        elif stream_info['platform'] == 'youtube':
            is_valid = stream_service.validate_youtube_video(stream_info['stream_id'])
            if not is_valid:
                error_message = 'YouTube video not found'
    except Exception as e:
        # If validation fails, assume valid but warn
        is_valid = True
        error_message = 'Could not validate with platform API'
    
    return jsonify({
        'valid': is_valid,
        'platform': stream_info['platform'],
        'stream_id': stream_info['stream_id'],
        'embed_url': stream_info['embed_url'],
        'error': error_message
    })


@bp.route('/tournament/<int:tournament_id>')
def tournament_streams(tournament_id):
    """View all streams for a tournament."""
    tournament = Tournament.query.get_or_404(tournament_id)
    streams = stream_service.get_tournament_streams(tournament_id, active_only=False)
    
    return render_template('streams/tournament_streams.html', 
                         tournament=tournament, 
                         streams=streams)


@bp.route('/match/<int:match_id>')
def match_streams(match_id):
    """View all streams for a match."""
    match = Match.query.get_or_404(match_id)
    streams = stream_service.get_match_streams(match_id, active_only=False)
    
    return render_template('streams/match_streams.html', 
                         match=match, 
                         streams=streams)
