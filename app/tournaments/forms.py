from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, IntegerField, DecimalField, DateTimeLocalField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Length, NumberRange, Optional, URL, ValidationError
from datetime import datetime, timedelta
from app.models import TournamentFormat, TournamentStatus


class TournamentForm(FlaskForm):
    """Form for creating and editing tournaments."""
    name = StringField('Tournament Name', validators=[
        DataRequired(),
        Length(min=3, max=200, message='Tournament name must be between 3 and 200 characters')
    ])
    
    description = TextAreaField('Description', validators=[
        Optional(),
        Length(max=2000, message='Description cannot exceed 2000 characters')
    ])
    
    game = SelectField('Game', validators=[DataRequired()], choices=[
        ('valorant', 'Valorant'),
        ('dota2', 'Dota 2'),
        ('csgo', 'CS:GO'),
        ('bgmi', 'BGMI'),
        ('lol', 'League of Legends'),
        ('apex', 'Apex Legends'),
        ('fortnite', 'Fortnite'),
        ('pubg', 'PUBG'),
        ('overwatch', 'Overwatch 2'),
        ('rocket_league', 'Rocket League'),
        ('fifa', 'FIFA'),
        ('other', 'Other')
    ])
    
    format = SelectField('Tournament Format', validators=[DataRequired()], choices=[
        (TournamentFormat.SINGLE_ELIMINATION.value, 'Single Elimination'),
        (TournamentFormat.DOUBLE_ELIMINATION.value, 'Double Elimination'),
        (TournamentFormat.ROUND_ROBIN.value, 'Round Robin'),
        (TournamentFormat.SWISS.value, 'Swiss System')
    ])
    
    max_teams = IntegerField('Maximum Teams', validators=[
        DataRequired(),
        NumberRange(min=2, max=256, message='Maximum teams must be between 2 and 256')
    ], default=16)
    
    team_size = IntegerField('Team Size', validators=[
        DataRequired(),
        NumberRange(min=1, max=10, message='Team size must be between 1 and 10 players')
    ], default=5)
    
    entry_fee = DecimalField('Entry Fee ($)', validators=[
        Optional(),
        NumberRange(min=0, message='Entry fee cannot be negative')
    ], default=0.00, places=2)
    
    prize_pool = DecimalField('Prize Pool ($)', validators=[
        Optional(),
        NumberRange(min=0, message='Prize pool cannot be negative')
    ], default=0.00, places=2)
    
    registration_start = DateTimeLocalField('Registration Start', validators=[DataRequired()],
                                          default=lambda: datetime.now().replace(second=0, microsecond=0))
    
    registration_end = DateTimeLocalField('Registration End', validators=[DataRequired()],
                                        default=lambda: datetime.now().replace(second=0, microsecond=0) + timedelta(days=7))
    
    tournament_start = DateTimeLocalField('Tournament Start', validators=[DataRequired()],
                                        default=lambda: datetime.now().replace(second=0, microsecond=0) + timedelta(days=14))
    
    tournament_end = DateTimeLocalField('Tournament End (Optional)', validators=[Optional()])
    
    rules = TextAreaField('Tournament Rules', validators=[
        Optional(),
        Length(max=5000, message='Rules cannot exceed 5000 characters')
    ])
    
    stream_url = StringField('Stream URL (Optional)', validators=[
        Optional(),
        URL(message='Please enter a valid URL'),
        Length(max=255, message='URL cannot exceed 255 characters')
    ])
    
    discord_server = StringField('Discord Server (Optional)', validators=[
        Optional(),
        Length(max=255, message='Discord server cannot exceed 255 characters')
    ])
    
    submit = SubmitField('Create Tournament')
    
    def validate_registration_end(self, field):
        """Validate that registration end is after registration start."""
        if field.data and self.registration_start.data:
            if field.data <= self.registration_start.data:
                raise ValidationError('Registration end must be after registration start.')
    
    def validate_tournament_start(self, field):
        """Validate that tournament start is after registration end."""
        if field.data and self.registration_end.data:
            if field.data <= self.registration_end.data:
                raise ValidationError('Tournament start must be after registration end.')
    
    def validate_tournament_end(self, field):
        """Validate that tournament end is after tournament start."""
        if field.data and self.tournament_start.data:
            if field.data <= self.tournament_start.data:
                raise ValidationError('Tournament end must be after tournament start.')


class TournamentEditForm(TournamentForm):
    """Form for editing existing tournaments."""
    status = SelectField('Tournament Status', validators=[DataRequired()], choices=[
        (TournamentStatus.DRAFT.value, 'Draft'),
        (TournamentStatus.REGISTRATION_OPEN.value, 'Registration Open'),
        (TournamentStatus.REGISTRATION_CLOSED.value, 'Registration Closed'),
        (TournamentStatus.IN_PROGRESS.value, 'In Progress'),
        (TournamentStatus.COMPLETED.value, 'Completed'),
        (TournamentStatus.CANCELLED.value, 'Cancelled')
    ])
    
    submit = SubmitField('Update Tournament')


class TeamRegistrationForm(FlaskForm):
    """Form for team registration to tournaments."""
    team_id = SelectField('Select Team', validators=[DataRequired()], coerce=int)
    
    submit = SubmitField('Register Team')


class QuickTournamentForm(FlaskForm):
    """Simplified form for quick tournament creation."""
    name = StringField('Tournament Name', validators=[
        DataRequired(),
        Length(min=3, max=200)
    ])
    
    game = SelectField('Game', validators=[DataRequired()], choices=[
        ('valorant', 'Valorant'),
        ('dota2', 'Dota 2'),
        ('csgo', 'CS:GO'),
        ('bgmi', 'BGMI'),
        ('lol', 'League of Legends'),
        ('fifa', 'FIFA')
    ])
    
    max_teams = SelectField('Maximum Teams', validators=[DataRequired()], choices=[
        ('8', '8 Teams'),
        ('16', '16 Teams'),
        ('32', '32 Teams'),
        ('64', '64 Teams')
    ], coerce=int, default=16)
    
    team_size = SelectField('Team Size', validators=[DataRequired()], choices=[
        ('1', '1v1'),
        ('2', '2v2'),
        ('3', '3v3'),
        ('5', '5v5'),
        ('6', '6v6')
    ], coerce=int, default=5)
    
    registration_days = SelectField('Registration Period', validators=[DataRequired()], choices=[
        ('3', '3 Days'),
        ('7', '1 Week'),
        ('14', '2 Weeks'),
        ('30', '1 Month')
    ], coerce=int, default=7)
    
    submit = SubmitField('Create Tournament')
