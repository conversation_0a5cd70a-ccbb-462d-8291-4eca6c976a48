from flask import render_template, redirect, url_for, flash, request, abort, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from app.tournaments import bp
from app.tournaments.forms import TournamentForm, TournamentEditForm, TeamRegistrationForm, QuickTournamentForm
from app.models import Tournament, TournamentTeam, Team, User, UserRole, TournamentStatus, TournamentFormat
from app import db
from app.services.discord_service import discord_service


@bp.route('/')
def index():
    """List all tournaments."""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    game_filter = request.args.get('game', 'all')

    # Build query
    query = Tournament.query.filter_by(is_active=True)

    # Apply filters
    if status_filter != 'all':
        try:
            status_enum = TournamentStatus(status_filter)
            query = query.filter_by(status=status_enum)
        except ValueError:
            pass

    if game_filter != 'all':
        query = query.filter_by(game=game_filter)

    # Order by creation date (newest first)
    query = query.order_by(Tournament.created_at.desc())

    # Paginate
    tournaments = query.paginate(
        page=page, per_page=12, error_out=False
    )

    # Get unique games for filter dropdown
    games = db.session.query(Tournament.game).distinct().all()
    games = [game[0] for game in games]

    return render_template('tournaments/index.html',
                         title='Tournaments',
                         tournaments=tournaments,
                         games=games,
                         status_filter=status_filter,
                         game_filter=game_filter)


@bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """Create new tournament."""
    # Check if user can create tournaments
    if current_user.role not in [UserRole.ORGANIZER, UserRole.ADMIN]:
        flash('You need to be an organizer or admin to create tournaments.', 'error')
        return redirect(url_for('tournaments.index'))

    form = TournamentForm()

    if form.validate_on_submit():
        tournament = Tournament(
            name=form.name.data,
            description=form.description.data,
            game=form.game.data,
            format=TournamentFormat(form.format.data),
            max_teams=form.max_teams.data,
            team_size=form.team_size.data,
            entry_fee=form.entry_fee.data,
            prize_pool=form.prize_pool.data,
            registration_start=form.registration_start.data,
            registration_end=form.registration_end.data,
            tournament_start=form.tournament_start.data,
            tournament_end=form.tournament_end.data,
            rules=form.rules.data,
            stream_url=form.stream_url.data,
            discord_server=form.discord_server.data,
            organizer_id=current_user.id,
            status=TournamentStatus.DRAFT
        )

        db.session.add(tournament)
        db.session.commit()

        # Send Discord notification
        discord_service.notify_tournament_created(tournament)

        flash(f'Tournament "{tournament.name}" created successfully!', 'success')
        return redirect(url_for('tournaments.view', id=tournament.id))

    return render_template('tournaments/create.html',
                         title='Create Tournament',
                         form=form)


@bp.route('/quick-create', methods=['GET', 'POST'])
@login_required
def quick_create():
    """Quick tournament creation with simplified form."""
    if current_user.role not in [UserRole.ORGANIZER, UserRole.ADMIN]:
        flash('You need to be an organizer or admin to create tournaments.', 'error')
        return redirect(url_for('tournaments.index'))

    form = QuickTournamentForm()

    if form.validate_on_submit():
        # Calculate dates based on registration period
        now = datetime.now().replace(second=0, microsecond=0)
        registration_start = now
        registration_end = now + timedelta(days=form.registration_days.data)
        tournament_start = registration_end + timedelta(days=1)

        tournament = Tournament(
            name=form.name.data,
            game=form.game.data,
            format=TournamentFormat.SINGLE_ELIMINATION,
            max_teams=form.max_teams.data,
            team_size=form.team_size.data,
            registration_start=registration_start,
            registration_end=registration_end,
            tournament_start=tournament_start,
            organizer_id=current_user.id,
            status=TournamentStatus.DRAFT
        )

        db.session.add(tournament)
        db.session.commit()

        # Send Discord notification
        discord_service.notify_tournament_created(tournament)

        flash(f'Tournament "{tournament.name}" created successfully!', 'success')
        return redirect(url_for('tournaments.view', id=tournament.id))

    return render_template('tournaments/quick_create.html',
                         title='Quick Create Tournament',
                         form=form)


@bp.route('/<int:id>')
def view(id):
    """View tournament details."""
    tournament = Tournament.query.get_or_404(id)

    # Get registered teams
    registered_teams = TournamentTeam.query.filter_by(
        tournament_id=id, is_active=True
    ).order_by(TournamentTeam.registered_at).all()

    # Check if current user can manage this tournament
    can_manage = (current_user.is_authenticated and
                 (current_user.id == tournament.organizer_id or
                  current_user.role == UserRole.ADMIN))

    # Check if current user can register a team
    can_register = (current_user.is_authenticated and
                   current_user.role == UserRole.PLAYER and
                   tournament.is_registration_open)

    return render_template('tournaments/view.html',
                         title=tournament.name,
                         tournament=tournament,
                         registered_teams=registered_teams,
                         can_manage=can_manage,
                         can_register=can_register)


@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """Edit tournament."""
    tournament = Tournament.query.get_or_404(id)

    # Check permissions
    if not (current_user.id == tournament.organizer_id or current_user.role == UserRole.ADMIN):
        flash('You do not have permission to edit this tournament.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    form = TournamentEditForm(obj=tournament)

    if form.validate_on_submit():
        tournament.name = form.name.data
        tournament.description = form.description.data
        tournament.game = form.game.data
        tournament.format = TournamentFormat(form.format.data)
        tournament.status = TournamentStatus(form.status.data)
        tournament.max_teams = form.max_teams.data
        tournament.team_size = form.team_size.data
        tournament.entry_fee = form.entry_fee.data
        tournament.prize_pool = form.prize_pool.data
        tournament.registration_start = form.registration_start.data
        tournament.registration_end = form.registration_end.data
        tournament.tournament_start = form.tournament_start.data
        tournament.tournament_end = form.tournament_end.data
        tournament.rules = form.rules.data
        tournament.stream_url = form.stream_url.data
        tournament.discord_server = form.discord_server.data
        tournament.updated_at = datetime.utcnow()

        db.session.commit()

        flash(f'Tournament "{tournament.name}" updated successfully!', 'success')
        return redirect(url_for('tournaments.view', id=id))

    return render_template('tournaments/edit.html',
                         title=f'Edit {tournament.name}',
                         form=form,
                         tournament=tournament)


@bp.route('/<int:id>/register', methods=['GET', 'POST'])
@login_required
def register_team(id):
    """Register a team for the tournament."""
    tournament = Tournament.query.get_or_404(id)

    # Check if registration is open
    if not tournament.is_registration_open:
        flash('Registration is not currently open for this tournament.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Check if user is a player
    if current_user.role != UserRole.PLAYER:
        flash('Only players can register teams for tournaments.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Get user's teams where they are captain
    user_teams = Team.query.filter_by(captain_id=current_user.player_profile.id, is_active=True).all()

    if not user_teams:
        flash('You need to be a team captain to register for tournaments.', 'error')
        return redirect(url_for('teams.create'))

    form = TeamRegistrationForm()
    form.team_id.choices = [(team.id, f"{team.name} ({team.tag})") for team in user_teams]

    if form.validate_on_submit():
        team_id = form.team_id.data

        # Check if team is already registered
        existing_registration = TournamentTeam.query.filter_by(
            tournament_id=id, team_id=team_id, is_active=True
        ).first()

        if existing_registration:
            flash('This team is already registered for the tournament.', 'error')
            return redirect(url_for('tournaments.view', id=id))

        # Check if tournament is full
        if tournament.registered_teams_count >= tournament.max_teams:
            flash('Tournament is full. Registration closed.', 'error')
            return redirect(url_for('tournaments.view', id=id))

        # Register the team
        registration = TournamentTeam(
            tournament_id=id,
            team_id=team_id
        )

        db.session.add(registration)
        db.session.commit()

        team = Team.query.get(team_id)
        flash(f'Team "{team.name}" successfully registered for "{tournament.name}"!', 'success')
        return redirect(url_for('tournaments.view', id=id))

    return render_template('tournaments/register.html',
                         title=f'Register for {tournament.name}',
                         form=form,
                         tournament=tournament)


@bp.route('/<int:id>/unregister/<int:team_id>', methods=['POST'])
@login_required
def unregister_team(id, team_id):
    """Unregister a team from the tournament."""
    tournament = Tournament.query.get_or_404(id)
    team = Team.query.get_or_404(team_id)

    # Check permissions
    if not (current_user.player_profile and
            (current_user.player_profile.id == team.captain_id or
             current_user.id == tournament.organizer_id or
             current_user.role == UserRole.ADMIN)):
        flash('You do not have permission to unregister this team.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Find registration
    registration = TournamentTeam.query.filter_by(
        tournament_id=id, team_id=team_id, is_active=True
    ).first()

    if not registration:
        flash('Team is not registered for this tournament.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Check if tournament has started
    if tournament.status in [TournamentStatus.IN_PROGRESS, TournamentStatus.COMPLETED]:
        flash('Cannot unregister from a tournament that has already started.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Unregister team
    registration.is_active = False
    db.session.commit()

    flash(f'Team "{team.name}" has been unregistered from "{tournament.name}".', 'success')
    return redirect(url_for('tournaments.view', id=id))


@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """Delete tournament."""
    tournament = Tournament.query.get_or_404(id)

    # Check permissions
    if not (current_user.id == tournament.organizer_id or current_user.role == UserRole.ADMIN):
        flash('You do not have permission to delete this tournament.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Check if tournament has started
    if tournament.status in [TournamentStatus.IN_PROGRESS, TournamentStatus.COMPLETED]:
        flash('Cannot delete a tournament that has already started or completed.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    tournament_name = tournament.name
    tournament.is_active = False
    db.session.commit()

    flash(f'Tournament "{tournament_name}" has been deleted.', 'success')
    return redirect(url_for('tournaments.index'))


@bp.route('/<int:id>/start', methods=['POST'])
@login_required
def start_tournament(id):
    """Start the tournament (change status to in progress)."""
    tournament = Tournament.query.get_or_404(id)

    # Check permissions
    if not (current_user.id == tournament.organizer_id or current_user.role == UserRole.ADMIN):
        flash('You do not have permission to start this tournament.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Check if tournament can be started
    if tournament.status != TournamentStatus.REGISTRATION_CLOSED:
        flash('Tournament must be in "Registration Closed" status to start.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Check if there are enough teams
    if tournament.registered_teams_count < 2:
        flash('Tournament needs at least 2 teams to start.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    try:
        # Generate bracket and schedule matches
        from app.services.match_scheduler import MatchScheduler
        scheduler = MatchScheduler(tournament)
        matches = scheduler.start_tournament()

        flash(f'Tournament "{tournament.name}" has been started! {len(matches)} matches generated.', 'success')
    except Exception as e:
        flash(f'Error starting tournament: {str(e)}', 'error')
        return redirect(url_for('tournaments.view', id=id))

    return redirect(url_for('tournaments.view', id=id))


@bp.route('/<int:id>/close-registration', methods=['POST'])
@login_required
def close_registration(id):
    """Close tournament registration."""
    tournament = Tournament.query.get_or_404(id)

    # Check permissions
    if not (current_user.id == tournament.organizer_id or current_user.role == UserRole.ADMIN):
        flash('You do not have permission to manage this tournament.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Check if registration can be closed
    if tournament.status != TournamentStatus.REGISTRATION_OPEN:
        flash('Registration is not currently open.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    tournament.status = TournamentStatus.REGISTRATION_CLOSED
    db.session.commit()

    flash(f'Registration for "{tournament.name}" has been closed.', 'success')
    return redirect(url_for('tournaments.view', id=id))


@bp.route('/<int:id>/open-registration', methods=['POST'])
@login_required
def open_registration(id):
    """Open tournament registration."""
    tournament = Tournament.query.get_or_404(id)

    # Check permissions
    if not (current_user.id == tournament.organizer_id or current_user.role == UserRole.ADMIN):
        flash('You do not have permission to manage this tournament.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Check if registration can be opened
    if tournament.status not in [TournamentStatus.DRAFT, TournamentStatus.REGISTRATION_CLOSED]:
        flash('Cannot open registration for this tournament.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    tournament.status = TournamentStatus.REGISTRATION_OPEN
    db.session.commit()

    flash(f'Registration for "{tournament.name}" is now open!', 'success')
    return redirect(url_for('tournaments.view', id=id))


@bp.route('/my-tournaments')
@login_required
def my_tournaments():
    """View tournaments organized by current user."""
    if current_user.role not in [UserRole.ORGANIZER, UserRole.ADMIN]:
        flash('You need to be an organizer or admin to view this page.', 'error')
        return redirect(url_for('tournaments.index'))

    page = request.args.get('page', 1, type=int)

    tournaments = Tournament.query.filter_by(
        organizer_id=current_user.id, is_active=True
    ).order_by(Tournament.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )

    return render_template('tournaments/my_tournaments.html',
                         title='My Tournaments',
                         tournaments=tournaments)


@bp.route('/<int:id>/bracket')
def view_bracket(id):
    """View tournament bracket."""
    tournament = Tournament.query.get_or_404(id)

    # Get tournament schedule
    from app.services.match_scheduler import MatchScheduler
    scheduler = MatchScheduler(tournament)
    schedule = scheduler.get_tournament_schedule()

    # Get next matches and live matches
    next_matches = scheduler.get_next_matches(5)
    live_matches = scheduler.get_live_matches()

    return render_template('tournaments/bracket.html',
                         title=f'{tournament.name} - Bracket',
                         tournament=tournament,
                         schedule=schedule,
                         next_matches=next_matches,
                         live_matches=live_matches)


@bp.route('/<int:id>/generate-next-round', methods=['POST'])
@login_required
def generate_next_round(id):
    """Generate next round for Swiss tournaments."""
    tournament = Tournament.query.get_or_404(id)

    # Check permissions
    if not (current_user.id == tournament.organizer_id or current_user.role == UserRole.ADMIN):
        flash('You do not have permission to manage this tournament.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    # Only for Swiss tournaments
    if tournament.format != TournamentFormat.SWISS:
        flash('Next round generation is only available for Swiss tournaments.', 'error')
        return redirect(url_for('tournaments.view', id=id))

    try:
        # Get current round number
        latest_match = Match.query.filter_by(tournament_id=id).order_by(Match.round_number.desc()).first()
        next_round = (latest_match.round_number + 1) if latest_match else 1

        # Generate next round
        from app.services.bracket_generator import generate_next_swiss_round
        new_matches = generate_next_swiss_round(tournament, next_round)

        # Save matches
        for match in new_matches:
            db.session.add(match)
        db.session.commit()

        flash(f'Round {next_round} generated with {len(new_matches)} matches!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error generating next round: {str(e)}', 'error')

    return redirect(url_for('tournaments.view_bracket', id=id))
