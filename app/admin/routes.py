"""Admin routes for dashboard and administrative functions."""
from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from sqlalchemy import func, desc
from datetime import datetime, timedelta

from app.admin import bp
from app.models import Tournament, Team, Player, Match, User, TournamentStatus, UserRole, Stream, MatchStatus
from app import db
import psutil
import os
from collections import defaultdict


def admin_required(f):
    """Decorator to require admin access."""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != UserRole.ADMIN:
            flash('Admin access required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function


@bp.route('/')
@bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """Enhanced admin dashboard with comprehensive analytics and monitoring."""

    # Calculate comprehensive statistics
    stats = {
        'total_tournaments': Tournament.query.count(),
        'active_tournaments': Tournament.query.filter(Tournament.status.in_([TournamentStatus.REGISTRATION_OPEN, TournamentStatus.IN_PROGRESS])).count(),
        'completed_tournaments': Tournament.query.filter_by(status=TournamentStatus.COMPLETED).count(),
        'active_users': User.query.filter_by(is_active=True).count(),
        'total_users': User.query.count(),
        'total_matches': Match.query.count(),
        'completed_matches': Match.query.filter_by(status=MatchStatus.COMPLETED).count(),
        'total_teams': Team.query.count(),
        'total_streams': Stream.query.count(),
        'active_streams': Stream.query.filter_by(is_active=True).count()
    }

    # Calculate growth metrics (last 30 days vs previous 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    sixty_days_ago = datetime.utcnow() - timedelta(days=60)

    growth_metrics = {
        'new_users_30d': User.query.filter(User.created_at >= thirty_days_ago).count(),
        'new_users_prev_30d': User.query.filter(User.created_at >= sixty_days_ago, User.created_at < thirty_days_ago).count(),
        'new_tournaments_30d': Tournament.query.filter(Tournament.created_at >= thirty_days_ago).count(),
        'new_tournaments_prev_30d': Tournament.query.filter(Tournament.created_at >= sixty_days_ago, Tournament.created_at < thirty_days_ago).count(),
        'matches_completed_30d': Match.query.filter(Match.completed_at >= thirty_days_ago).count() if Match.query.filter(Match.completed_at >= thirty_days_ago).first() else 0,
    }

    # Calculate growth percentages
    for metric in ['new_users', 'new_tournaments']:
        current = growth_metrics[f'{metric}_30d']
        previous = growth_metrics[f'{metric}_prev_30d']
        if previous > 0:
            growth_metrics[f'{metric}_growth'] = round(((current - previous) / previous) * 100, 1)
        else:
            growth_metrics[f'{metric}_growth'] = 100 if current > 0 else 0

    # Get recent tournaments (last 10)
    recent_tournaments = Tournament.query.order_by(desc(Tournament.created_at)).limit(10).all()

    # Get system health metrics
    system_health = get_system_health()

    # Generate recent activities
    recent_activities = get_recent_activities()

    # Tournament status distribution
    tournament_status_data = db.session.query(
        Tournament.status, func.count(Tournament.id)
    ).group_by(Tournament.status).all()

    tournament_status_chart = {
        'labels': [status.value.replace('_', ' ').title() for status, count in tournament_status_data],
        'data': [count for status, count in tournament_status_data]
    }

    # User role distribution
    user_role_data = db.session.query(
        User.role, func.count(User.id)
    ).group_by(User.role).all()

    user_role_chart = {
        'labels': [role.value.title() for role, count in user_role_data],
        'data': [count for role, count in user_role_data]
    }

    # Match completion rate over time (last 7 days)
    match_completion_data = []
    for i in range(7):
        date = datetime.utcnow() - timedelta(days=i)
        completed = Match.query.filter(
            func.date(Match.completed_at) == date.date()
        ).count() if Match.query.filter(func.date(Match.completed_at) == date.date()).first() else 0
        match_completion_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'completed': completed
        })

    match_completion_chart = {
        'labels': [item['date'] for item in reversed(match_completion_data)],
        'data': [item['completed'] for item in reversed(match_completion_data)]
    }

    return render_template('admin/dashboard.html',
                         stats=stats,
                         growth_metrics=growth_metrics,
                         recent_tournaments=recent_tournaments,
                         recent_activities=recent_activities,
                         system_health=system_health,
                         tournament_status_chart=tournament_status_chart,
                         user_role_chart=user_role_chart,
                         match_completion_chart=match_completion_chart)


def get_system_health():
    """Get system health metrics."""
    try:
        # CPU and Memory usage
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # Database connection count (simplified)
        db_connections = 1  # Placeholder for actual DB connection monitoring

        return {
            'cpu_usage': cpu_percent,
            'memory_usage': memory.percent,
            'memory_available': round(memory.available / (1024**3), 2),  # GB
            'disk_usage': disk.percent,
            'disk_free': round(disk.free / (1024**3), 2),  # GB
            'db_connections': db_connections,
            'status': 'healthy' if cpu_percent < 80 and memory.percent < 80 and disk.percent < 90 else 'warning'
        }
    except Exception as e:
        return {
            'cpu_usage': 0,
            'memory_usage': 0,
            'memory_available': 0,
            'disk_usage': 0,
            'disk_free': 0,
            'db_connections': 0,
            'status': 'error',
            'error': str(e)
        }


def get_recent_activities():
    """Get recent system activities."""
    activities = []

    # Recent tournaments
    recent_tournaments = Tournament.query.order_by(desc(Tournament.created_at)).limit(3).all()
    for tournament in recent_tournaments:
        activities.append({
            'title': 'New Tournament Created',
            'description': f'Tournament "{tournament.name}" was created by {tournament.organizer.username}',
            'timestamp': tournament.created_at,
            'type': 'success',
            'icon': 'fas fa-trophy'
        })

    # Recent user registrations
    recent_users = User.query.order_by(desc(User.created_at)).limit(3).all()
    for user in recent_users:
        activities.append({
            'title': 'New User Registration',
            'description': f'User "{user.username}" registered as {user.role.value}',
            'timestamp': user.created_at,
            'type': 'info',
            'icon': 'fas fa-user-plus'
        })

    # Recent completed matches
    recent_matches = Match.query.filter(Match.status == MatchStatus.COMPLETED).order_by(desc(Match.completed_at)).limit(3).all()
    for match in recent_matches:
        activities.append({
            'title': 'Match Completed',
            'description': f'{match.teams_display} in {match.tournament.name}',
            'timestamp': match.completed_at,
            'type': 'primary',
            'icon': 'fas fa-gamepad'
        })

    # Sort by timestamp and return latest 10
    activities.sort(key=lambda x: x['timestamp'], reverse=True)
    return activities[:10]


@bp.route('/analytics')
@login_required
@admin_required
def analytics():
    """Advanced analytics page with detailed charts and metrics."""

    # Tournament analytics
    tournament_analytics = get_tournament_analytics()

    # User analytics
    user_analytics = get_user_analytics()

    # Match analytics
    match_analytics = get_match_analytics()

    # Stream analytics
    stream_analytics = get_stream_analytics()

    return render_template('admin/analytics.html',
                         tournament_analytics=tournament_analytics,
                         user_analytics=user_analytics,
                         match_analytics=match_analytics,
                         stream_analytics=stream_analytics)


def get_tournament_analytics():
    """Get detailed tournament analytics."""
    # Tournament creation over time (last 30 days)
    creation_data = []
    for i in range(30):
        date = datetime.utcnow() - timedelta(days=i)
        count = Tournament.query.filter(func.date(Tournament.created_at) == date.date()).count()
        creation_data.append({'date': date.strftime('%Y-%m-%d'), 'count': count})

    # Tournament completion rate
    total_tournaments = Tournament.query.count()
    completed_tournaments = Tournament.query.filter_by(status=TournamentStatus.COMPLETED).count()
    completion_rate = round((completed_tournaments / total_tournaments * 100), 1) if total_tournaments > 0 else 0

    # Average tournament duration
    completed = Tournament.query.filter_by(status=TournamentStatus.COMPLETED).all()
    avg_duration = 0
    if completed:
        durations = []
        for t in completed:
            if t.start_date and t.end_date:
                duration = (t.end_date - t.start_date).days
                durations.append(duration)
        avg_duration = round(sum(durations) / len(durations), 1) if durations else 0

    return {
        'creation_over_time': {
            'labels': [item['date'] for item in reversed(creation_data)],
            'data': [item['count'] for item in reversed(creation_data)]
        },
        'completion_rate': completion_rate,
        'avg_duration_days': avg_duration,
        'total_tournaments': total_tournaments,
        'completed_tournaments': completed_tournaments
    }


def get_user_analytics():
    """Get detailed user analytics."""
    # User registration over time (last 30 days)
    registration_data = []
    for i in range(30):
        date = datetime.utcnow() - timedelta(days=i)
        count = User.query.filter(func.date(User.created_at) == date.date()).count()
        registration_data.append({'date': date.strftime('%Y-%m-%d'), 'count': count})

    # User activity (users who created tournaments or joined teams in last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    active_users = User.query.filter(
        (User.tournaments.any(Tournament.created_at >= thirty_days_ago)) |
        (User.created_at >= thirty_days_ago)
    ).count()

    total_users = User.query.count()
    activity_rate = round((active_users / total_users * 100), 1) if total_users > 0 else 0

    return {
        'registration_over_time': {
            'labels': [item['date'] for item in reversed(registration_data)],
            'data': [item['count'] for item in reversed(registration_data)]
        },
        'activity_rate': activity_rate,
        'active_users': active_users,
        'total_users': total_users
    }


def get_match_analytics():
    """Get detailed match analytics."""
    # Match completion over time (last 30 days)
    completion_data = []
    for i in range(30):
        date = datetime.utcnow() - timedelta(days=i)
        count = Match.query.filter(
            func.date(Match.completed_at) == date.date()
        ).count() if Match.query.filter(func.date(Match.completed_at) == date.date()).first() else 0
        completion_data.append({'date': date.strftime('%Y-%m-%d'), 'count': count})

    # Match status distribution
    status_data = db.session.query(
        Match.status, func.count(Match.id)
    ).group_by(Match.status).all()

    # Average match duration (placeholder - would need actual timing data)
    avg_duration = 45  # minutes

    return {
        'completion_over_time': {
            'labels': [item['date'] for item in reversed(completion_data)],
            'data': [item['count'] for item in reversed(completion_data)]
        },
        'status_distribution': {
            'labels': [status.value.replace('_', ' ').title() for status, count in status_data],
            'data': [count for status, count in status_data]
        },
        'avg_duration_minutes': avg_duration
    }


def get_stream_analytics():
    """Get detailed stream analytics."""
    # Stream platform distribution
    platform_data = db.session.query(
        Stream.platform, func.count(Stream.id)
    ).group_by(Stream.platform).all()

    # Active vs inactive streams
    active_streams = Stream.query.filter_by(is_active=True).count()
    total_streams = Stream.query.count()

    return {
        'platform_distribution': {
            'labels': [platform.title() for platform, count in platform_data],
            'data': [count for platform, count in platform_data]
        },
        'active_streams': active_streams,
        'total_streams': total_streams,
        'activity_rate': round((active_streams / total_streams * 100), 1) if total_streams > 0 else 0
    }


@bp.route('/users')
@login_required
@admin_required
def user_management():
    """User management page with advanced filtering and actions."""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # Get filter parameters
    role_filter = request.args.get('role', '')
    status_filter = request.args.get('status', '')
    search_query = request.args.get('search', '')

    # Build query
    query = User.query

    if role_filter:
        query = query.filter(User.role == UserRole[role_filter.upper()])

    if status_filter == 'active':
        query = query.filter_by(is_active=True)
    elif status_filter == 'inactive':
        query = query.filter_by(is_active=False)

    if search_query:
        query = query.filter(
            (User.username.contains(search_query)) |
            (User.email.contains(search_query))
        )

    # Order by creation date (newest first)
    query = query.order_by(desc(User.created_at))

    # Paginate
    users = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    # Get user statistics
    user_stats = {
        'total_users': User.query.count(),
        'active_users': User.query.filter_by(is_active=True).count(),
        'admin_users': User.query.filter_by(role=UserRole.ADMIN).count(),
        'organizer_users': User.query.filter_by(role=UserRole.ORGANIZER).count(),
        'player_users': User.query.filter_by(role=UserRole.PLAYER).count(),
    }

    return render_template('admin/user_management.html',
                         users=users,
                         user_stats=user_stats,
                         role_filter=role_filter,
                         status_filter=status_filter,
                         search_query=search_query)


@bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """Toggle user active status."""
    user = User.query.get_or_404(user_id)

    if user.id == current_user.id:
        flash('You cannot deactivate your own account.', 'error')
        return redirect(url_for('admin.user_management'))

    user.is_active = not user.is_active
    db.session.commit()

    status = 'activated' if user.is_active else 'deactivated'
    flash(f'User {user.username} has been {status}.', 'success')

    return redirect(url_for('admin.user_management'))


@bp.route('/users/<int:user_id>/change-role', methods=['POST'])
@login_required
@admin_required
def change_user_role(user_id):
    """Change user role."""
    user = User.query.get_or_404(user_id)
    new_role = request.form.get('role')

    if user.id == current_user.id:
        flash('You cannot change your own role.', 'error')
        return redirect(url_for('admin.user_management'))

    try:
        user.role = UserRole[new_role.upper()]
        db.session.commit()
        flash(f'User {user.username} role changed to {new_role}.', 'success')
    except (KeyError, ValueError):
        flash('Invalid role specified.', 'error')

    return redirect(url_for('admin.user_management'))


@bp.route('/system-health')
@login_required
@admin_required
def system_health():
    """System health monitoring page."""
    health_data = get_system_health()

    # Get database statistics
    db_stats = {
        'total_tournaments': Tournament.query.count(),
        'total_users': User.query.count(),
        'total_matches': Match.query.count(),
        'total_teams': Team.query.count(),
        'total_streams': Stream.query.count(),
    }

    # Get recent errors (placeholder - would integrate with logging system)
    recent_errors = [
        {
            'timestamp': datetime.utcnow() - timedelta(hours=1),
            'level': 'WARNING',
            'message': 'High CPU usage detected',
            'source': 'System Monitor'
        },
        {
            'timestamp': datetime.utcnow() - timedelta(hours=3),
            'level': 'INFO',
            'message': 'Database backup completed successfully',
            'source': 'Backup Service'
        }
    ]

    return render_template('admin/system_health.html',
                         health_data=health_data,
                         db_stats=db_stats,
                         recent_errors=recent_errors)


@bp.route('/api/system-metrics')
@login_required
@admin_required
def api_system_metrics():
    """API endpoint for real-time system metrics."""
    return jsonify(get_system_health())


@bp.route('/tournament-monitoring')
@login_required
@admin_required
def tournament_monitoring():
    """Tournament monitoring and management page."""
    # Get tournaments by status
    tournaments_by_status = {
        'draft': Tournament.query.filter_by(status=TournamentStatus.DRAFT).all(),
        'registration_open': Tournament.query.filter_by(status=TournamentStatus.REGISTRATION_OPEN).all(),
        'registration_closed': Tournament.query.filter_by(status=TournamentStatus.REGISTRATION_CLOSED).all(),
        'in_progress': Tournament.query.filter_by(status=TournamentStatus.IN_PROGRESS).all(),
        'completed': Tournament.query.filter_by(status=TournamentStatus.COMPLETED).order_by(desc(Tournament.end_date)).limit(10).all(),
    }

    # Get tournaments with issues (placeholder logic)
    problematic_tournaments = []
    for tournament in Tournament.query.filter(Tournament.status.in_([TournamentStatus.IN_PROGRESS, TournamentStatus.REGISTRATION_CLOSED])).all():
        issues = []

        # Check for tournaments with no matches
        if tournament.status == TournamentStatus.IN_PROGRESS and not tournament.matches:
            issues.append('No matches scheduled')

        # Check for overdue tournaments
        if tournament.end_date and tournament.end_date < datetime.utcnow() and tournament.status != TournamentStatus.COMPLETED:
            issues.append('Overdue completion')

        # Check for tournaments with few participants
        if len(tournament.teams) < 2:
            issues.append('Insufficient participants')

        if issues:
            problematic_tournaments.append({
                'tournament': tournament,
                'issues': issues
            })

    return render_template('admin/tournament_monitoring.html',
                         tournaments_by_status=tournaments_by_status,
                         problematic_tournaments=problematic_tournaments)


@bp.route('/reports')
@login_required
@admin_required
def reports():
    """Comprehensive reporting page."""
    # Generate various reports
    reports_data = {
        'tournament_report': generate_tournament_report(),
        'user_report': generate_user_report(),
        'match_report': generate_match_report(),
        'financial_report': generate_financial_report()
    }

    return render_template('admin/reports.html', reports_data=reports_data)


def generate_tournament_report():
    """Generate tournament summary report."""
    total = Tournament.query.count()
    completed = Tournament.query.filter_by(status=TournamentStatus.COMPLETED).count()
    in_progress = Tournament.query.filter_by(status=TournamentStatus.IN_PROGRESS).count()

    # Average participants per tournament
    tournaments_with_teams = Tournament.query.join(Tournament.teams).all()
    avg_participants = 0
    if tournaments_with_teams:
        total_participants = sum(len(t.teams) for t in tournaments_with_teams)
        avg_participants = round(total_participants / len(tournaments_with_teams), 1)

    return {
        'total_tournaments': total,
        'completed_tournaments': completed,
        'in_progress_tournaments': in_progress,
        'completion_rate': round((completed / total * 100), 1) if total > 0 else 0,
        'avg_participants': avg_participants
    }


def generate_user_report():
    """Generate user summary report."""
    total = User.query.count()
    active = User.query.filter_by(is_active=True).count()

    # User growth (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    new_users = User.query.filter(User.created_at >= thirty_days_ago).count()

    return {
        'total_users': total,
        'active_users': active,
        'new_users_30d': new_users,
        'activity_rate': round((active / total * 100), 1) if total > 0 else 0
    }


def generate_match_report():
    """Generate match summary report."""
    total = Match.query.count()
    completed = Match.query.filter_by(status=MatchStatus.COMPLETED).count()

    return {
        'total_matches': total,
        'completed_matches': completed,
        'completion_rate': round((completed / total * 100), 1) if total > 0 else 0
    }


def generate_financial_report():
    """Generate financial summary report (placeholder)."""
    # This would integrate with actual payment processing
    tournaments_with_fees = Tournament.query.filter(Tournament.entry_fee > 0).all()
    total_revenue = sum(t.entry_fee * len(t.teams) for t in tournaments_with_fees)

    return {
        'total_revenue': total_revenue,
        'tournaments_with_fees': len(tournaments_with_fees),
        'avg_entry_fee': round(sum(t.entry_fee for t in tournaments_with_fees) / len(tournaments_with_fees), 2) if tournaments_with_fees else 0
    }


@bp.route('/users')
@login_required
@admin_required
def users():
    """User management page."""
    page = request.args.get('page', 1, type=int)
    role_filter = request.args.get('role', 'all')
    
    query = User.query
    
    if role_filter != 'all':
        query = query.filter_by(role=UserRole(role_filter))
    
    users = query.order_by(desc(User.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/users.html',
                         title='User Management',
                         users=users,
                         role_filter=role_filter)


@bp.route('/tournaments')
@login_required
@admin_required
def tournaments():
    """Tournament management page."""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    
    query = Tournament.query
    
    if status_filter != 'all':
        query = query.filter_by(status=TournamentStatus(status_filter))
    
    tournaments = query.order_by(desc(Tournament.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/tournaments.html',
                         title='Tournament Management',
                         tournaments=tournaments,
                         status_filter=status_filter)




@bp.route('/settings', methods=['GET', 'POST'])
@login_required
@admin_required
def settings():
    """System settings page."""
    if request.method == 'POST':
        # Handle settings update
        flash('Settings updated successfully!', 'success')
        return redirect(url_for('admin.settings'))
    
    return render_template('admin/settings.html',
                         title='System Settings')


@bp.route('/api/stats')
@login_required
@admin_required
def api_stats():
    """API endpoint for dashboard statistics."""
    stats = {
        'total_tournaments': Tournament.query.count(),
        'active_users': User.query.filter_by(is_active=True).count(),
        'total_matches': Match.query.count(),
        'total_teams': Team.query.count(),
        'tournaments_by_status': {}
    }
    
    # Tournament counts by status
    for status in TournamentStatus:
        count = Tournament.query.filter_by(status=status).count()
        stats['tournaments_by_status'][status.value] = count
    
    return jsonify(stats)


@bp.route('/api/tournament-trends')
@login_required
@admin_required
def api_tournament_trends():
    """API endpoint for tournament creation trends."""
    days = request.args.get('days', 30, type=int)
    start_date = datetime.utcnow() - timedelta(days=days)
    
    trends = db.session.query(
        func.date(Tournament.created_at).label('date'),
        func.count(Tournament.id).label('count')
    ).filter(Tournament.created_at >= start_date)\
     .group_by(func.date(Tournament.created_at))\
     .order_by('date').all()
    
    data = {
        'labels': [trend.date.strftime('%Y-%m-%d') for trend in trends],
        'data': [trend.count for trend in trends]
    }
    
    return jsonify(data)


@bp.route('/tournament/<int:tournament_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
def toggle_tournament_status(tournament_id):
    """Toggle tournament status."""
    tournament = Tournament.query.get_or_404(tournament_id)
    
    # Simple status cycling for demo
    status_cycle = {
        TournamentStatus.DRAFT: TournamentStatus.REGISTRATION_OPEN,
        TournamentStatus.REGISTRATION_OPEN: TournamentStatus.REGISTRATION_CLOSED,
        TournamentStatus.REGISTRATION_CLOSED: TournamentStatus.IN_PROGRESS,
        TournamentStatus.IN_PROGRESS: TournamentStatus.COMPLETED,
        TournamentStatus.COMPLETED: TournamentStatus.DRAFT
    }
    
    tournament.status = status_cycle.get(tournament.status, TournamentStatus.DRAFT)
    db.session.commit()
    
    flash(f'Tournament "{tournament.name}" status updated to {tournament.status.value}.', 'success')
    
    return redirect(url_for('admin.tournaments'))
