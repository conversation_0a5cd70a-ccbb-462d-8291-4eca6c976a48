from datetime import datetime, timezone
from decimal import Decimal
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from bson import ObjectId
from pymongo import MongoClient
import enum


class UserRole(enum.Enum):
    """User roles in the system."""
    PLAYER = "player"
    ORGANIZER = "organizer"
    ADMIN = "admin"


class TournamentStatus(enum.Enum):
    """Tournament status options."""
    DRAFT = "draft"
    REGISTRATION_OPEN = "registration_open"
    REGISTRATION_CLOSED = "registration_closed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TournamentFormat(enum.Enum):
    """Tournament bracket formats."""
    SINGLE_ELIMINATION = "single_elimination"
    DOUBLE_ELIMINATION = "double_elimination"
    ROUND_ROBIN = "round_robin"
    SWISS = "swiss"


class MatchStatus(enum.Enum):
    """Match status options."""
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    DISPUTED = "disputed"


class BaseModel:
    """Base model class for MongoDB documents."""
    
    def __init__(self, collection_name, db):
        self.collection_name = collection_name
        self.db = db
        self.collection = db[collection_name]
    
    def create_indexes(self):
        """Create indexes for the collection. Override in subclasses."""
        pass
    
    def validate_document(self, document):
        """Validate document before insertion. Override in subclasses."""
        return True
    
    def insert_one(self, document):
        """Insert a single document."""
        if self.validate_document(document):
            document['created_at'] = datetime.utcnow()
            document['updated_at'] = datetime.utcnow()
            return self.collection.insert_one(document)
        raise ValueError("Document validation failed")
    
    def find_one(self, filter_dict=None, **kwargs):
        """Find a single document."""
        return self.collection.find_one(filter_dict, **kwargs)
    
    def find(self, filter_dict=None, **kwargs):
        """Find multiple documents."""
        return self.collection.find(filter_dict, **kwargs)
    
    def update_one(self, filter_dict, update_dict, **kwargs):
        """Update a single document."""
        update_dict.setdefault('$set', {})['updated_at'] = datetime.utcnow()
        return self.collection.update_one(filter_dict, update_dict, **kwargs)
    
    def delete_one(self, filter_dict):
        """Delete a single document."""
        return self.collection.delete_one(filter_dict)
    
    def count_documents(self, filter_dict=None):
        """Count documents matching filter."""
        return self.collection.count_documents(filter_dict or {})


class User(UserMixin, BaseModel):
    """User model for authentication and profiles."""
    
    def __init__(self, db):
        super().__init__('users', db)
    
    def create_indexes(self):
        """Create indexes for users collection."""
        self.collection.create_index("username", unique=True)
        self.collection.create_index("email", unique=True)
        self.collection.create_index("role")
        self.collection.create_index("is_active")
    
    def validate_document(self, document):
        """Validate user document."""
        required_fields = ['username', 'email', 'password_hash', 'first_name', 'last_name']
        return all(field in document for field in required_fields)
    
    @staticmethod
    def set_password(password):
        """Generate password hash."""
        return generate_password_hash(password)
    
    @staticmethod
    def check_password(password_hash, password):
        """Check password against hash."""
        return check_password_hash(password_hash, password)
    
    def get_id(self):
        """Get user ID for Flask-Login."""
        return str(self._id) if hasattr(self, '_id') else None
    
    def create_user(self, username, email, password, first_name, last_name, 
                   role=UserRole.PLAYER.value, **kwargs):
        """Create a new user."""
        user_doc = {
            'username': username,
            'email': email,
            'password_hash': self.set_password(password),
            'first_name': first_name,
            'last_name': last_name,
            'role': role,
            'is_active': kwargs.get('is_active', True),
            'avatar_url': kwargs.get('avatar_url'),
            'bio': kwargs.get('bio'),
            'discord_username': kwargs.get('discord_username'),
            'twitch_username': kwargs.get('twitch_username')
        }
        return self.insert_one(user_doc)
    
    def find_by_username(self, username):
        """Find user by username."""
        return self.find_one({'username': username})
    
    def find_by_email(self, email):
        """Find user by email."""
        return self.find_one({'email': email})
    
    def find_by_id(self, user_id):
        """Find user by ID."""
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)
        return self.find_one({'_id': user_id})


class Tournament(BaseModel):
    """Tournament model."""
    
    def __init__(self, db):
        super().__init__('tournaments', db)
    
    def create_indexes(self):
        """Create indexes for tournaments collection."""
        self.collection.create_index("name")
        self.collection.create_index("game")
        self.collection.create_index("status")
        self.collection.create_index("organizer_id")
        self.collection.create_index("registration_start")
        self.collection.create_index("tournament_start")
        self.collection.create_index("is_active")
    
    def validate_document(self, document):
        """Validate tournament document."""
        required_fields = ['name', 'game', 'organizer_id', 'registration_start', 
                          'registration_end', 'tournament_start']
        return all(field in document for field in required_fields)
    
    def create_tournament(self, name, game, organizer_id, registration_start, 
                         registration_end, tournament_start, **kwargs):
        """Create a new tournament."""
        tournament_doc = {
            'name': name,
            'description': kwargs.get('description'),
            'game': game,
            'format': kwargs.get('format', TournamentFormat.SINGLE_ELIMINATION.value),
            'status': kwargs.get('status', TournamentStatus.DRAFT.value),
            'max_teams': kwargs.get('max_teams', 16),
            'team_size': kwargs.get('team_size', 5),
            'entry_fee': float(kwargs.get('entry_fee', 0.00)),
            'prize_pool': float(kwargs.get('prize_pool', 0.00)),
            'registration_start': registration_start,
            'registration_end': registration_end,
            'tournament_start': tournament_start,
            'tournament_end': kwargs.get('tournament_end'),
            'organizer_id': ObjectId(organizer_id) if isinstance(organizer_id, str) else organizer_id,
            'is_active': kwargs.get('is_active', True),
            'rules': kwargs.get('rules'),
            'stream_url': kwargs.get('stream_url'),
            'discord_server': kwargs.get('discord_server')
        }
        return self.insert_one(tournament_doc)
    
    def find_by_organizer(self, organizer_id):
        """Find tournaments by organizer."""
        if isinstance(organizer_id, str):
            organizer_id = ObjectId(organizer_id)
        return self.find({'organizer_id': organizer_id})
    
    def find_active(self):
        """Find active tournaments."""
        return self.find({'is_active': True})


class Team(BaseModel):
    """Team model."""
    
    def __init__(self, db):
        super().__init__('teams', db)
    
    def create_indexes(self):
        """Create indexes for teams collection."""
        self.collection.create_index("name")
        self.collection.create_index("tag", unique=True)
        self.collection.create_index("captain_id")
        self.collection.create_index("is_active")
    
    def validate_document(self, document):
        """Validate team document."""
        required_fields = ['name', 'tag']
        return all(field in document for field in required_fields)
    
    def create_team(self, name, tag, **kwargs):
        """Create a new team."""
        team_doc = {
            'name': name,
            'tag': tag,
            'description': kwargs.get('description'),
            'logo_url': kwargs.get('logo_url'),
            'captain_id': ObjectId(kwargs['captain_id']) if kwargs.get('captain_id') else None,
            'is_active': kwargs.get('is_active', True),
            'player_ids': kwargs.get('player_ids', [])  # Store player IDs directly
        }
        return self.insert_one(team_doc)
    
    def add_player(self, team_id, player_id):
        """Add a player to a team."""
        if isinstance(team_id, str):
            team_id = ObjectId(team_id)
        if isinstance(player_id, str):
            player_id = ObjectId(player_id)
        
        return self.update_one(
            {'_id': team_id},
            {'$addToSet': {'player_ids': player_id}}
        )
    
    def remove_player(self, team_id, player_id):
        """Remove a player from a team."""
        if isinstance(team_id, str):
            team_id = ObjectId(team_id)
        if isinstance(player_id, str):
            player_id = ObjectId(player_id)
        
        return self.update_one(
            {'_id': team_id},
            {'$pull': {'player_ids': player_id}}
        )


class Player(BaseModel):
    """Player model - extends User with gaming-specific information."""
    
    def __init__(self, db):
        super().__init__('players', db)
    
    def create_indexes(self):
        """Create indexes for players collection."""
        self.collection.create_index("user_id", unique=True)
        self.collection.create_index("team_id")
        self.collection.create_index("in_game_name")
        self.collection.create_index("is_active")
    
    def validate_document(self, document):
        """Validate player document."""
        required_fields = ['user_id', 'in_game_name']
        return all(field in document for field in required_fields)
    
    def create_player(self, user_id, in_game_name, **kwargs):
        """Create a new player profile."""
        player_doc = {
            'user_id': ObjectId(user_id) if isinstance(user_id, str) else user_id,
            'team_id': ObjectId(kwargs['team_id']) if kwargs.get('team_id') else None,
            'in_game_name': in_game_name,
            'preferred_games': kwargs.get('preferred_games'),
            'skill_level': kwargs.get('skill_level'),
            'matches_played': kwargs.get('matches_played', 0),
            'matches_won': kwargs.get('matches_won', 0),
            'tournaments_participated': kwargs.get('tournaments_participated', 0),
            'tournaments_won': kwargs.get('tournaments_won', 0),
            'is_active': kwargs.get('is_active', True)
        }
        return self.insert_one(player_doc)
    
    def find_by_user_id(self, user_id):
        """Find player by user ID."""
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)
        return self.find_one({'user_id': user_id})
    
    def find_by_team(self, team_id):
        """Find players by team."""
        if isinstance(team_id, str):
            team_id = ObjectId(team_id)
        return self.find({'team_id': team_id})


class TournamentTeam(BaseModel):
    """Association model for tournament team registrations."""

    def __init__(self, db):
        super().__init__('tournament_teams', db)

    def create_indexes(self):
        """Create indexes for tournament_teams collection."""
        self.collection.create_index([("tournament_id", 1), ("team_id", 1)], unique=True)
        self.collection.create_index("tournament_id")
        self.collection.create_index("team_id")
        self.collection.create_index("is_active")

    def validate_document(self, document):
        """Validate tournament team document."""
        required_fields = ['tournament_id', 'team_id']
        return all(field in document for field in required_fields)

    def register_team(self, tournament_id, team_id, **kwargs):
        """Register a team for a tournament."""
        registration_doc = {
            'tournament_id': ObjectId(tournament_id) if isinstance(tournament_id, str) else tournament_id,
            'team_id': ObjectId(team_id) if isinstance(team_id, str) else team_id,
            'registered_at': kwargs.get('registered_at', datetime.utcnow()),
            'is_active': kwargs.get('is_active', True),
            'seed': kwargs.get('seed'),
            'placement': kwargs.get('placement'),
            'prize_won': float(kwargs.get('prize_won', 0.00))
        }
        return self.insert_one(registration_doc)

    def find_by_tournament(self, tournament_id):
        """Find teams registered for a tournament."""
        if isinstance(tournament_id, str):
            tournament_id = ObjectId(tournament_id)
        return self.find({'tournament_id': tournament_id})

    def find_by_team(self, team_id):
        """Find tournaments a team is registered for."""
        if isinstance(team_id, str):
            team_id = ObjectId(team_id)
        return self.find({'team_id': team_id})


class Match(BaseModel):
    """Match model for tournament matches."""

    def __init__(self, db):
        super().__init__('matches', db)

    def create_indexes(self):
        """Create indexes for matches collection."""
        self.collection.create_index("tournament_id")
        self.collection.create_index("round_number")
        self.collection.create_index("status")
        self.collection.create_index("scheduled_time")
        self.collection.create_index("team1_id")
        self.collection.create_index("team2_id")

    def validate_document(self, document):
        """Validate match document."""
        required_fields = ['tournament_id', 'round_number', 'match_number']
        return all(field in document for field in required_fields)

    def create_match(self, tournament_id, round_number, match_number, **kwargs):
        """Create a new match."""
        match_doc = {
            'tournament_id': ObjectId(tournament_id) if isinstance(tournament_id, str) else tournament_id,
            'round_number': round_number,
            'match_number': match_number,
            'bracket_position': kwargs.get('bracket_position'),
            'team1_id': ObjectId(kwargs['team1_id']) if kwargs.get('team1_id') else None,
            'team2_id': ObjectId(kwargs['team2_id']) if kwargs.get('team2_id') else None,
            'winner_id': ObjectId(kwargs['winner_id']) if kwargs.get('winner_id') else None,
            'best_of': kwargs.get('best_of', 1),
            'status': kwargs.get('status', MatchStatus.SCHEDULED.value),
            'scheduled_time': kwargs.get('scheduled_time'),
            'started_at': kwargs.get('started_at'),
            'completed_at': kwargs.get('completed_at'),
            'team1_score': kwargs.get('team1_score', 0),
            'team2_score': kwargs.get('team2_score', 0),
            'stream_url': kwargs.get('stream_url'),
            'notes': kwargs.get('notes')
        }
        return self.insert_one(match_doc)

    def find_by_tournament(self, tournament_id):
        """Find matches by tournament."""
        if isinstance(tournament_id, str):
            tournament_id = ObjectId(tournament_id)
        return self.find({'tournament_id': tournament_id})

    def find_by_team(self, team_id):
        """Find matches involving a team."""
        if isinstance(team_id, str):
            team_id = ObjectId(team_id)
        return self.find({'$or': [{'team1_id': team_id}, {'team2_id': team_id}]})


class Game(BaseModel):
    """Individual game within a match (for best-of-X matches)."""

    def __init__(self, db):
        super().__init__('games', db)

    def create_indexes(self):
        """Create indexes for games collection."""
        self.collection.create_index("match_id")
        self.collection.create_index("game_number")
        self.collection.create_index("winner_id")

    def validate_document(self, document):
        """Validate game document."""
        required_fields = ['match_id', 'game_number']
        return all(field in document for field in required_fields)

    def create_game(self, match_id, game_number, **kwargs):
        """Create a new game."""
        game_doc = {
            'match_id': ObjectId(match_id) if isinstance(match_id, str) else match_id,
            'game_number': game_number,
            'team1_score': kwargs.get('team1_score'),
            'team2_score': kwargs.get('team2_score'),
            'winner_id': ObjectId(kwargs['winner_id']) if kwargs.get('winner_id') else None,
            'map_played': kwargs.get('map_played'),
            'duration': kwargs.get('duration'),
            'started_at': kwargs.get('started_at'),
            'completed_at': kwargs.get('completed_at'),
            'game_data': kwargs.get('game_data')
        }
        return self.insert_one(game_doc)

    def find_by_match(self, match_id):
        """Find games by match."""
        if isinstance(match_id, str):
            match_id = ObjectId(match_id)
        return self.find({'match_id': match_id})


class Stream(BaseModel):
    """Stream model for tournament and match streaming."""

    def __init__(self, db):
        super().__init__('streams', db)

    def create_indexes(self):
        """Create indexes for streams collection."""
        self.collection.create_index("tournament_id")
        self.collection.create_index("match_id")
        self.collection.create_index("platform")
        self.collection.create_index("is_active")

    def validate_document(self, document):
        """Validate stream document."""
        required_fields = ['tournament_id', 'platform', 'stream_id', 'stream_url', 'embed_url']
        return all(field in document for field in required_fields)

    def create_stream(self, tournament_id, platform, stream_id, stream_url, embed_url, **kwargs):
        """Create a new stream."""
        stream_doc = {
            'tournament_id': ObjectId(tournament_id) if isinstance(tournament_id, str) else tournament_id,
            'match_id': ObjectId(kwargs['match_id']) if kwargs.get('match_id') else None,
            'platform': platform,
            'stream_id': stream_id,
            'stream_url': stream_url,
            'embed_url': embed_url,
            'title': kwargs.get('title'),
            'description': kwargs.get('description'),
            'is_active': kwargs.get('is_active', True)
        }
        return self.insert_one(stream_doc)

    def find_by_tournament(self, tournament_id):
        """Find streams by tournament."""
        if isinstance(tournament_id, str):
            tournament_id = ObjectId(tournament_id)
        return self.find({'tournament_id': tournament_id})

    def find_active(self):
        """Find active streams."""
        return self.find({'is_active': True})
