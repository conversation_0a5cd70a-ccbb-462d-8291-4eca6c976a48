"""Initial migration

Revision ID: 1063b697e51b
Revises: 
Create Date: 2025-07-02 12:25:45.118583

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1063b697e51b'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('players',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('user_id', sa.Integer(), nullable=False),
    sa.Column('team_id', sa.Integer(), nullable=True),
    sa.Column('in_game_name', sa.String(length=100), nullable=False),
    sa.Column('preferred_games', sa.String(length=255), nullable=True),
    sa.Column('skill_level', sa.String(length=50), nullable=True),
    sa.Column('matches_played', sa.Integer(), nullable=True),
    sa.Column('matches_won', sa.Integer(), nullable=True),
    sa.Column('tournaments_participated', sa.Integer(), nullable=True),
    sa.Column('tournaments_won', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('joined_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('teams',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('tag', sa.String(length=10), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('logo_url', sa.String(length=255), nullable=True),
    sa.Column('captain_id', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['captain_id'], ['players.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=80), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('first_name', sa.String(length=50), nullable=False),
    sa.Column('last_name', sa.String(length=50), nullable=False),
    sa.Column('role', sa.Enum('PLAYER', 'ORGANIZER', 'ADMIN', name='userrole'), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('avatar_url', sa.String(length=255), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('discord_username', sa.String(length=100), nullable=True),
    sa.Column('twitch_username', sa.String(length=100), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_username'), ['username'], unique=True)

    op.create_table('tournaments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('game', sa.String(length=100), nullable=False),
    sa.Column('format', sa.Enum('SINGLE_ELIMINATION', 'DOUBLE_ELIMINATION', 'ROUND_ROBIN', 'SWISS', name='tournamentformat'), nullable=True),
    sa.Column('status', sa.Enum('DRAFT', 'REGISTRATION_OPEN', 'REGISTRATION_CLOSED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', name='tournamentstatus'), nullable=True),
    sa.Column('max_teams', sa.Integer(), nullable=False),
    sa.Column('team_size', sa.Integer(), nullable=False),
    sa.Column('entry_fee', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('prize_pool', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('registration_start', sa.DateTime(), nullable=False),
    sa.Column('registration_end', sa.DateTime(), nullable=False),
    sa.Column('tournament_start', sa.DateTime(), nullable=False),
    sa.Column('tournament_end', sa.DateTime(), nullable=True),
    sa.Column('organizer_id', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('rules', sa.Text(), nullable=True),
    sa.Column('stream_url', sa.String(length=255), nullable=True),
    sa.Column('discord_server', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['organizer_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('tournament_teams',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tournament_id', sa.Integer(), nullable=False),
    sa.Column('team_id', sa.Integer(), nullable=False),
    sa.Column('registered_at', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('seed', sa.Integer(), nullable=True),
    sa.Column('placement', sa.Integer(), nullable=True),
    sa.Column('prize_won', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ),
    sa.ForeignKeyConstraint(['tournament_id'], ['tournaments.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tournament_id', 'team_id', name='unique_tournament_team')
    )
    op.create_table('matches',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tournament_id', sa.Integer(), nullable=False),
    sa.Column('round_number', sa.Integer(), nullable=False),
    sa.Column('match_number', sa.Integer(), nullable=False),
    sa.Column('bracket_position', sa.String(length=50), nullable=True),
    sa.Column('team1_id', sa.Integer(), nullable=True),
    sa.Column('team2_id', sa.Integer(), nullable=True),
    sa.Column('winner_id', sa.Integer(), nullable=True),
    sa.Column('best_of', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'DISPUTED', name='matchstatus'), nullable=True),
    sa.Column('scheduled_time', sa.DateTime(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('team1_score', sa.Integer(), nullable=True),
    sa.Column('team2_score', sa.Integer(), nullable=True),
    sa.Column('stream_url', sa.String(length=255), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['team1_id'], ['tournament_teams.id'], ),
    sa.ForeignKeyConstraint(['team2_id'], ['tournament_teams.id'], ),
    sa.ForeignKeyConstraint(['tournament_id'], ['tournaments.id'], ),
    sa.ForeignKeyConstraint(['winner_id'], ['tournament_teams.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('games',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('match_id', sa.Integer(), nullable=False),
    sa.Column('game_number', sa.Integer(), nullable=False),
    sa.Column('team1_score', sa.Integer(), nullable=True),
    sa.Column('team2_score', sa.Integer(), nullable=True),
    sa.Column('winner_id', sa.Integer(), nullable=True),
    sa.Column('map_played', sa.String(length=100), nullable=True),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('game_data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['match_id'], ['matches.id'], ),
    sa.ForeignKeyConstraint(['winner_id'], ['tournament_teams.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('games')
    op.drop_table('matches')
    op.drop_table('tournament_teams')
    op.drop_table('tournaments')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_username'))
        batch_op.drop_index(batch_op.f('ix_users_email'))

    op.drop_table('users')
    op.drop_table('teams')
    op.drop_table('players')
    # ### end Alembic commands ###
