import os
from app import create_app, db, socketio
from app.models import User, Tournament, Team, Player, Match, TournamentTeam
from flask_migrate import upgrade


app = create_app(os.getenv('FLASK_CONFIG') or 'default')


@app.shell_context_processor
def make_shell_context():
    """Make database models available in flask shell."""
    return {
        'db': db,
        'User': User,
        'Tournament': Tournament,
        'Team': Team,
        'Player': Player,
        'Match': Match,
        'TournamentTeam': TournamentTeam
    }


@app.cli.command()
def deploy():
    """Run deployment tasks."""
    # Create database tables
    upgrade()


if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5001)
