#!/usr/bin/env python3
"""
Debug registration issues by creating a user directly in the database.
"""
from app import create_app, db
from app.models import User, Player, UserR<PERSON>

def create_test_user():
    """Create a test user directly in the database."""
    app = create_app()
    
    with app.app_context():
        # Check if user already exists
        existing_user = User.query.filter_by(username='testplayer').first()
        if existing_user:
            print("Test user already exists!")
            print(f"User: {existing_user.username} ({existing_user.email})")
            print(f"Role: {existing_user.role.value}")
            if existing_user.player_profile:
                print(f"Player profile: {existing_user.player_profile.in_game_name}")
            return existing_user
        
        # Create new user
        user = User(
            username='testplayer',
            email='<EMAIL>',
            first_name='Test',
            last_name='Player',
            role=UserRole.PLAYER
        )
        user.set_password('testpassword123')
        
        db.session.add(user)
        db.session.commit()
        
        # Create player profile
        player = Player(
            user_id=user.id,
            in_game_name='testplayer'
        )
        db.session.add(player)
        db.session.commit()
        
        print("✓ Test user created successfully!")
        print(f"User: {user.username} ({user.email})")
        print(f"Role: {user.role.value}")
        print(f"Player profile: {player.in_game_name}")
        
        return user

def test_password():
    """Test password hashing and verification."""
    app = create_app()
    
    with app.app_context():
        user = User.query.filter_by(username='testplayer').first()
        if not user:
            print("No test user found!")
            return False
        
        # Test password verification
        if user.check_password('testpassword123'):
            print("✓ Password verification works!")
            return True
        else:
            print("✗ Password verification failed!")
            return False

if __name__ == "__main__":
    print("Creating test user...")
    user = create_test_user()
    
    print("\nTesting password...")
    test_password()
    
    print("\nYou can now test login with:")
    print("Username: testplayer")
    print("Password: testpassword123")
