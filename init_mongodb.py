#!/usr/bin/env python3
"""
MongoDB Database Initialization Script

This script initializes the MongoDB database with collections and indexes
for the esports tournament management system.
"""

import os
from datetime import datetime
from app import create_app
from app.mongo_models import UserRole


def init_database():
    """Initialize MongoDB database with collections and indexes."""
    app = create_app(os.getenv('FLASK_CONFIG') or 'default')
    
    with app.app_context():
        print("Initializing MongoDB database...")
        
        # Get MongoDB models
        models = app.mongo_models
        
        # Create indexes for all collections
        print("Creating indexes...")
        for model_name, model in models.items():
            print(f"  - Creating indexes for {model_name}")
            model.create_indexes()
        
        print("Database initialization completed successfully!")
        
        # Create a default admin user if it doesn't exist
        user_model = models['User']
        admin_user = user_model.find_by_username('admin')
        
        if not admin_user:
            print("Creating default admin user...")
            try:
                result = user_model.create_user(
                    username='admin',
                    email='<EMAIL>',
                    password='admin123',  # Change this in production!
                    first_name='Admin',
                    last_name='User',
                    role=UserRole.ADMIN.value,
                    bio='Default administrator account'
                )
                print(f"Admin user created with ID: {result.inserted_id}")
            except Exception as e:
                print(f"Error creating admin user: {e}")
        else:
            print("Admin user already exists.")
        
        # Display collection statistics
        print("\nCollection Statistics:")
        from app import mongo
        db = mongo.db
        for collection_name in db.list_collection_names():
            count = db[collection_name].count_documents({})
            print(f"  - {collection_name}: {count} documents")


def drop_database():
    """Drop all collections in the database. Use with caution!"""
    app = create_app(os.getenv('FLASK_CONFIG') or 'default')
    
    with app.app_context():
        from app import mongo
        db = mongo.db
        
        print("WARNING: This will delete all data in the database!")
        confirm = input("Type 'DELETE' to confirm: ")
        
        if confirm == 'DELETE':
            print("Dropping all collections...")
            for collection_name in db.list_collection_names():
                db.drop_collection(collection_name)
                print(f"  - Dropped {collection_name}")
            print("Database cleared successfully!")
        else:
            print("Operation cancelled.")


def create_sample_data():
    """Create sample data for testing."""
    app = create_app(os.getenv('FLASK_CONFIG') or 'default')
    
    with app.app_context():
        models = app.mongo_models
        
        print("Creating sample data...")
        
        # Create sample users
        user_model = models['User']
        player_model = models['Player']
        team_model = models['Team']
        tournament_model = models['Tournament']
        
        # Sample users
        sample_users = [
            {
                'username': 'player1',
                'email': '<EMAIL>',
                'password': 'password123',
                'first_name': 'John',
                'last_name': 'Doe',
                'role': UserRole.PLAYER.value
            },
            {
                'username': 'player2',
                'email': '<EMAIL>',
                'password': 'password123',
                'first_name': 'Jane',
                'last_name': 'Smith',
                'role': UserRole.PLAYER.value
            },
            {
                'username': 'organizer1',
                'email': '<EMAIL>',
                'password': 'password123',
                'first_name': 'Mike',
                'last_name': 'Johnson',
                'role': UserRole.ORGANIZER.value
            }
        ]
        
        user_ids = []
        for user_data in sample_users:
            existing_user = user_model.find_by_username(user_data['username'])
            if not existing_user:
                result = user_model.create_user(**user_data)
                user_ids.append(result.inserted_id)
                print(f"Created user: {user_data['username']}")
            else:
                user_ids.append(existing_user['_id'])
                print(f"User already exists: {user_data['username']}")
        
        # Create sample players
        for i, user_id in enumerate(user_ids[:2]):  # First two users are players
            existing_player = player_model.find_by_user_id(user_id)
            if not existing_player:
                player_model.create_player(
                    user_id=user_id,
                    in_game_name=f'Player{i+1}',
                    preferred_games='Valorant, CS:GO',
                    skill_level='Intermediate'
                )
                print(f"Created player profile for user {user_id}")
        
        # Create sample team
        existing_team = team_model.find_one({'name': 'Sample Team'})
        if not existing_team:
            team_result = team_model.create_team(
                name='Sample Team',
                tag='SMPL',
                description='A sample team for testing',
                player_ids=user_ids[:2]  # Add first two players
            )
            print(f"Created team: Sample Team")
        
        # Create sample tournament
        organizer_id = user_ids[2]  # Third user is organizer
        existing_tournament = tournament_model.find_one({'name': 'Sample Tournament'})
        if not existing_tournament:
            tournament_model.create_tournament(
                name='Sample Tournament',
                game='Valorant',
                organizer_id=organizer_id,
                registration_start=datetime.utcnow(),
                registration_end=datetime.utcnow(),
                tournament_start=datetime.utcnow(),
                description='A sample tournament for testing',
                max_teams=16,
                team_size=5,
                prize_pool=1000.00
            )
            print("Created tournament: Sample Tournament")
        
        print("Sample data creation completed!")


if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == 'init':
            init_database()
        elif command == 'drop':
            drop_database()
        elif command == 'sample':
            create_sample_data()
        else:
            print("Usage: python init_mongodb.py [init|drop|sample]")
            print("  init   - Initialize database with indexes")
            print("  drop   - Drop all collections (WARNING: deletes all data)")
            print("  sample - Create sample data for testing")
    else:
        init_database()
