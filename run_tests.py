#!/usr/bin/env python3
"""Test runner script for the Esports Tournament Management System."""
import os
import sys
import subprocess
import argparse


def run_tests(test_path=None, coverage=False, verbose=False):
    """Run tests with optional coverage and verbosity."""
    
    # Ensure we're in the project directory
    project_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(project_dir)
    
    # Build pytest command
    cmd = ['python', '-m', 'pytest']
    
    if verbose:
        cmd.append('-v')
    
    if coverage:
        cmd.extend([
            '--cov=app',
            '--cov-report=html',
            '--cov-report=term-missing',
            '--cov-fail-under=80'
        ])
    
    if test_path:
        cmd.append(test_path)
    else:
        cmd.append('tests/')
    
    # Add additional pytest options
    cmd.extend([
        '--tb=short',  # Shorter traceback format
        '--strict-markers',  # Strict marker checking
        '-ra'  # Show all test results summary
    ])
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def install_test_dependencies():
    """Install test dependencies."""
    print("Installing test dependencies...")
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements-test.txt'
        ], check=True)
        print("Test dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install test dependencies: {e}")
        return False


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description='Run tests for Esports Tournament Management System')
    parser.add_argument('test_path', nargs='?', help='Specific test file or directory to run')
    parser.add_argument('-c', '--coverage', action='store_true', help='Run with coverage reporting')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    parser.add_argument('--install-deps', action='store_true', help='Install test dependencies')
    parser.add_argument('--models', action='store_true', help='Run only model tests')
    parser.add_argument('--auth', action='store_true', help='Run only authentication tests')
    parser.add_argument('--tournaments', action='store_true', help='Run only tournament tests')
    parser.add_argument('--services', action='store_true', help='Run only service tests')
    
    args = parser.parse_args()
    
    if args.install_deps:
        if not install_test_dependencies():
            return 1
    
    # Determine test path based on arguments
    test_path = args.test_path
    
    if args.models:
        test_path = 'tests/test_models.py'
    elif args.auth:
        test_path = 'tests/test_auth.py'
    elif args.tournaments:
        test_path = 'tests/test_tournaments.py'
    elif args.services:
        test_path = 'tests/test_services.py'
    
    return run_tests(test_path, args.coverage, args.verbose)


if __name__ == '__main__':
    sys.exit(main())
