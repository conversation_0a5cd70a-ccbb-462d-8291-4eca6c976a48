# Esports Tournament Management System

A comprehensive platform to create, manage, and track esports tournaments for games like Valorant, Dota, BGMI, and more.

## Features

### Core Features
- ✅ Create and manage tournaments
- ✅ Team registration & player profiles
- ✅ Match scheduling and results tracking
- ✅ Bracket generation (single/double elimination)
- ✅ Leaderboard and statistics tracking
- ✅ User authentication and role management

### Enhanced Features
- 🚀 Discord integration for team notifications
- 🚀 Admin panel for match officials
- 🚀 Live match score updates (WebSockets)
- 🚀 Twitch/YouTube stream embedding

## Tech Stack

- **Backend**: Flask + SQLAlchemy
- **Database**: PostgreSQL (SQLite for development)
- **Authentication**: Flask-Login + JWT
- **Real-time**: Flask-SocketIO
- **Frontend**: HTML5, CSS3, JavaScript (with bracket visualization libraries)
- **Testing**: pytest

## Quick Start

### Prerequisites
- Python 3.8+
- PostgreSQL (optional, SQLite works for development)
- Node.js (for frontend dependencies)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd esports
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Initialize the database:
```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

6. Run the application:
```bash
python app.py
```

The application will be available at `http://localhost:5000`

## Project Structure

```
esports/
├── app/
│   ├── __init__.py          # Application factory
│   ├── models.py            # Database models
│   ├── auth/                # Authentication blueprint
│   ├── main/                # Main pages blueprint
│   ├── tournaments/         # Tournament management
│   ├── teams/               # Team management
│   ├── matches/             # Match management
│   ├── api/                 # REST API endpoints
│   ├── static/              # CSS, JS, images
│   └── templates/           # HTML templates
├── migrations/              # Database migrations
├── tests/                   # Test files
├── config.py               # Configuration settings
├── requirements.txt        # Python dependencies
└── app.py                 # Application entry point
```

## Development

### Running Tests
```bash
pytest
```

### Database Migrations
```bash
flask db migrate -m "Description of changes"
flask db upgrade
```

### Code Formatting
```bash
black .
flake8 .
```

## API Documentation

The API endpoints will be documented here as they are implemented.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
