"""Test configuration and fixtures."""
import pytest
import tempfile
import os
from app import create_app, db
from app.models import User, Tournament, Team, Player, Match, UserRole, TournamentStatus, TournamentFormat
from datetime import datetime, timedelta


@pytest.fixture
def app():
    """Create application for testing."""
    # Create a temporary database file
    db_fd, db_path = tempfile.mkstemp()

    # Create test configuration
    test_config = {
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': f'sqlite:///{db_path}',
        'WTF_CSRF_ENABLED': False,
        'SECRET_KEY': 'test-secret-key',
        'SQLALCHEMY_TRACK_MODIFICATIONS': False
    }

    app = create_app()
    app.config.update(test_config)

    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def runner(app):
    """Create test CLI runner."""
    return app.test_cli_runner()


@pytest.fixture
def admin_user(app):
    """Create admin user for testing."""
    with app.app_context():
        user = User(
            username='admin',
            email='<EMAIL>',
            first_name='Admin',
            last_name='User',
            role=UserRole.ADMIN
        )
        user.set_password('password')
        db.session.add(user)
        db.session.commit()
        return user


@pytest.fixture
def organizer_user(app):
    """Create organizer user for testing."""
    with app.app_context():
        user = User(
            username='organizer',
            email='<EMAIL>',
            first_name='Organizer',
            last_name='User',
            role=UserRole.ORGANIZER
        )
        user.set_password('password')
        db.session.add(user)
        db.session.commit()

        # Refresh to ensure the user is attached to the session
        db.session.refresh(user)
        user_id = user.id

        # Return a fresh instance
        return User.query.get(user_id)


@pytest.fixture
def player_user(app):
    """Create player user for testing."""
    with app.app_context():
        user = User(
            username='player',
            email='<EMAIL>',
            first_name='Player',
            last_name='User',
            role=UserRole.PLAYER
        )
        user.set_password('password')
        db.session.add(user)
        db.session.commit()

        # Create player profile
        player = Player(user_id=user.id, in_game_name='TestPlayer')
        db.session.add(player)
        db.session.commit()

        # Return fresh instance
        user_id = user.id
        return User.query.get(user_id)


@pytest.fixture
def sample_tournament(app, organizer_user):
    """Create sample tournament for testing."""
    with app.app_context():
        # Get fresh organizer user from database
        organizer = User.query.filter_by(username='organizer').first()

        tournament = Tournament(
            name='Test Tournament',
            description='A test tournament',
            game='valorant',
            format=TournamentFormat.SINGLE_ELIMINATION,
            max_teams=8,
            registration_start=datetime.utcnow() + timedelta(days=1),
            registration_end=datetime.utcnow() + timedelta(days=3),
            tournament_start=datetime.utcnow() + timedelta(days=7),
            organizer_id=organizer.id,
            status=TournamentStatus.REGISTRATION_OPEN
        )
        db.session.add(tournament)
        db.session.commit()

        # Return fresh instance
        tournament_id = tournament.id
        return Tournament.query.get(tournament_id)


@pytest.fixture
def sample_teams(app, sample_tournament, player_user):
    """Create sample teams for testing."""
    with app.app_context():
        # Get fresh instances from database
        tournament = Tournament.query.filter_by(name='Test Tournament').first()
        player = User.query.filter_by(username='player').first()

        # Create player profile if it doesn't exist
        player_profile = Player.query.filter_by(user_id=player.id).first()
        if not player_profile:
            player_profile = Player(user_id=player.id, in_game_name='TestPlayer')
            db.session.add(player_profile)
            db.session.commit()

        teams = []
        for i in range(4):
            team = Team(
                name=f'Team {i+1}',
                tag=f'T{i+1}',
                description=f'Test team {i+1}',
                captain_id=player_profile.id
            )
            db.session.add(team)
            teams.append(team)

        db.session.commit()

        # Register teams to tournament using TournamentTeam association
        from app.models import TournamentTeam
        for team in teams:
            tournament_team = TournamentTeam(
                tournament_id=tournament.id,
                team_id=team.id
            )
            db.session.add(tournament_team)

        db.session.commit()

        # Return fresh instances
        team_ids = [team.id for team in teams]
        return [Team.query.get(team_id) for team_id in team_ids]


@pytest.fixture
def authenticated_client(client, organizer_user):
    """Create authenticated client session."""
    with client.session_transaction() as sess:
        sess['_user_id'] = str(organizer_user.id)
        sess['_fresh'] = True
    return client


def login_user(client, username='organizer', password='password'):
    """Helper function to log in a user."""
    return client.post('/auth/login', data={
        'username': username,
        'password': password
    }, follow_redirects=True)


def logout_user(client):
    """Helper function to log out a user."""
    return client.get('/auth/logout', follow_redirects=True)
