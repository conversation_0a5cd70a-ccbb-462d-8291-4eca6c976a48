"""Test tournament functionality."""
import pytest
from datetime import datetime, timedelta
from app.models import Tournament, Team, Match, TournamentStatus, TournamentFormat
from app import db
from tests.conftest import login_user


class TestTournamentRoutes:
    """Test tournament routes and views."""
    
    def test_tournament_list_page(self, client):
        """Test tournament listing page."""
        response = client.get('/tournaments/')
        assert response.status_code == 200
        assert b'Tournaments' in response.data
    
    def test_tournament_detail_page(self, client, sample_tournament):
        """Test tournament detail page."""
        response = client.get(f'/tournaments/{sample_tournament.id}')
        assert response.status_code == 200
        assert sample_tournament.name.encode() in response.data
    
    def test_tournament_create_page_requires_auth(self, client):
        """Test tournament creation requires authentication."""
        response = client.get('/tournaments/create')
        assert response.status_code == 302
        assert '/auth/login' in response.location
    
    def test_tournament_create_page_authenticated(self, client, organizer_user):
        """Test tournament creation page for authenticated organizer."""
        login_user(client, 'organizer', 'password')
        response = client.get('/tournaments/create')
        assert response.status_code == 200
        assert b'Create Tournament' in response.data


class TestTournamentCreation:
    """Test tournament creation functionality."""
    
    def test_create_tournament_success(self, client, organizer_user, app):
        """Test successful tournament creation."""
        login_user(client, 'organizer', 'password')
        
        tournament_data = {
            'name': 'New Tournament',
            'description': 'A new test tournament',
            'game': 'valorant',
            'format': 'single_elimination',
            'max_teams': '16',
            'start_date': (datetime.utcnow() + timedelta(days=7)).strftime('%Y-%m-%dT%H:%M'),
            'registration_deadline': (datetime.utcnow() + timedelta(days=3)).strftime('%Y-%m-%dT%H:%M'),
            'prize_pool': '1000'
        }
        
        response = client.post('/tournaments/create', data=tournament_data, follow_redirects=True)
        assert response.status_code == 200
        
        # Check tournament was created in database
        with app.app_context():
            tournament = Tournament.query.filter_by(name='New Tournament').first()
            assert tournament is not None
            assert tournament.organizer_id == organizer_user.id
            assert tournament.game == 'valorant'
            assert tournament.format == TournamentFormat.SINGLE_ELIMINATION
    
    def test_create_tournament_validation(self, client, organizer_user):
        """Test tournament creation validation."""
        login_user(client, 'organizer', 'password')
        
        # Test with invalid data (past date)
        tournament_data = {
            'name': 'Invalid Tournament',
            'description': 'Invalid tournament',
            'game': 'valorant',
            'format': 'single_elimination',
            'max_teams': '16',
            'start_date': (datetime.utcnow() - timedelta(days=1)).strftime('%Y-%m-%dT%H:%M'),
            'registration_deadline': (datetime.utcnow() - timedelta(days=2)).strftime('%Y-%m-%dT%H:%M')
        }
        
        response = client.post('/tournaments/create', data=tournament_data)
        assert response.status_code == 200
        # Should show validation error


class TestTournamentRegistration:
    """Test tournament registration functionality."""
    
    def test_team_registration_page(self, client, sample_tournament, player_user):
        """Test team registration page."""
        login_user(client, 'player', 'password')
        response = client.get(f'/tournaments/{sample_tournament.id}/register')
        assert response.status_code == 200
        assert b'Register Team' in response.data
    
    def test_team_registration_success(self, client, sample_tournament, player_user, app):
        """Test successful team registration."""
        login_user(client, 'player', 'password')
        
        team_data = {
            'name': 'New Team',
            'description': 'A new test team'
        }
        
        response = client.post(f'/tournaments/{sample_tournament.id}/register', 
                             data=team_data, follow_redirects=True)
        assert response.status_code == 200
        
        # Check team was created in database
        with app.app_context():
            team = Team.query.filter_by(name='New Team').first()
            assert team is not None
            assert team.tournament_id == sample_tournament.id
            assert team.captain_id == player_user.id
    
    def test_duplicate_team_registration(self, client, sample_tournament, player_user):
        """Test duplicate team registration prevention."""
        login_user(client, 'player', 'password')
        
        # First registration
        team_data = {
            'name': 'Test Team',
            'description': 'A test team'
        }
        client.post(f'/tournaments/{sample_tournament.id}/register', data=team_data)
        
        # Second registration (should fail)
        response = client.post(f'/tournaments/{sample_tournament.id}/register', 
                             data=team_data, follow_redirects=True)
        assert response.status_code == 200
        # Should show error message about already being registered


class TestTournamentBrackets:
    """Test tournament bracket functionality."""
    
    def test_bracket_page_loads(self, client, sample_tournament):
        """Test bracket page loads correctly."""
        response = client.get(f'/tournaments/{sample_tournament.id}/bracket')
        assert response.status_code == 200
        assert b'Tournament Bracket' in response.data
    
    def test_bracket_generation(self, client, sample_tournament, sample_teams, organizer_user, app):
        """Test bracket generation functionality."""
        login_user(client, 'organizer', 'password')
        
        # Generate bracket
        response = client.post(f'/tournaments/{sample_tournament.id}/generate-bracket', 
                             follow_redirects=True)
        assert response.status_code == 200
        
        # Check matches were created
        with app.app_context():
            matches = Match.query.filter_by(tournament_id=sample_tournament.id).all()
            assert len(matches) > 0
            
            # For 4 teams in single elimination, should have 3 matches total
            # (2 semifinals + 1 final)
            assert len(matches) == 3
    
    def test_bracket_without_teams(self, client, organizer_user, app):
        """Test bracket generation without enough teams."""
        login_user(client, 'organizer', 'password')
        
        with app.app_context():
            # Create tournament without teams
            tournament = Tournament(
                name='Empty Tournament',
                description='Tournament without teams',
                game='valorant',
                format=TournamentFormat.SINGLE_ELIMINATION,
                max_teams=8,
                start_date=datetime.utcnow() + timedelta(days=7),
                registration_deadline=datetime.utcnow() + timedelta(days=3),
                organizer_id=organizer_user.id
            )
            db.session.add(tournament)
            db.session.commit()
            
            # Try to generate bracket
            response = client.post(f'/tournaments/{tournament.id}/generate-bracket', 
                                 follow_redirects=True)
            assert response.status_code == 200
            # Should show error about insufficient teams


class TestTournamentManagement:
    """Test tournament management functionality."""
    
    def test_tournament_edit_page(self, client, sample_tournament, organizer_user):
        """Test tournament edit page."""
        login_user(client, 'organizer', 'password')
        response = client.get(f'/tournaments/{sample_tournament.id}/edit')
        assert response.status_code == 200
        assert b'Edit Tournament' in response.data
    
    def test_tournament_edit_success(self, client, sample_tournament, organizer_user, app):
        """Test successful tournament editing."""
        login_user(client, 'organizer', 'password')
        
        edit_data = {
            'name': 'Updated Tournament Name',
            'description': 'Updated description',
            'game': 'dota2',
            'format': 'round_robin',
            'max_teams': '12',
            'start_date': (datetime.utcnow() + timedelta(days=10)).strftime('%Y-%m-%dT%H:%M'),
            'registration_deadline': (datetime.utcnow() + timedelta(days=5)).strftime('%Y-%m-%dT%H:%M')
        }
        
        response = client.post(f'/tournaments/{sample_tournament.id}/edit', 
                             data=edit_data, follow_redirects=True)
        assert response.status_code == 200
        
        # Check tournament was updated
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            assert tournament.name == 'Updated Tournament Name'
            assert tournament.game == 'dota2'
            assert tournament.format == TournamentFormat.ROUND_ROBIN
    
    def test_tournament_delete(self, client, sample_tournament, organizer_user, app):
        """Test tournament deletion."""
        login_user(client, 'organizer', 'password')
        
        response = client.post(f'/tournaments/{sample_tournament.id}/delete', 
                             follow_redirects=True)
        assert response.status_code == 200
        
        # Check tournament was deleted
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            assert tournament is None
    
    def test_unauthorized_tournament_edit(self, client, sample_tournament, player_user):
        """Test unauthorized tournament editing."""
        login_user(client, 'player', 'password')
        
        response = client.get(f'/tournaments/{sample_tournament.id}/edit')
        assert response.status_code == 302  # Should redirect
    
    def test_edit_other_users_tournament(self, client, sample_tournament, app):
        """Test editing another user's tournament."""
        # Create another organizer
        with app.app_context():
            from app.models import User, UserRole
            other_organizer = User(
                username='other_organizer',
                email='<EMAIL>',
                first_name='Other',
                last_name='Organizer',
                role=UserRole.ORGANIZER
            )
            other_organizer.set_password('password')
            db.session.add(other_organizer)
            db.session.commit()
        
        login_user(client, 'other_organizer', 'password')
        
        response = client.get(f'/tournaments/{sample_tournament.id}/edit')
        assert response.status_code == 302  # Should redirect (unauthorized)


class TestTournamentStatus:
    """Test tournament status management."""
    
    def test_tournament_status_transitions(self, client, sample_tournament, organizer_user, app):
        """Test tournament status transitions."""
        login_user(client, 'organizer', 'password')
        
        # Start with REGISTRATION_OPEN status
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            assert tournament.status == TournamentStatus.REGISTRATION_OPEN
        
        # Close registration
        response = client.post(f'/tournaments/{sample_tournament.id}/close-registration', 
                             follow_redirects=True)
        assert response.status_code == 200
        
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            assert tournament.status == TournamentStatus.REGISTRATION_CLOSED
    
    def test_tournament_start(self, client, sample_tournament, organizer_user, app):
        """Test tournament start functionality."""
        login_user(client, 'organizer', 'password')
        
        # First close registration
        client.post(f'/tournaments/{sample_tournament.id}/close-registration')
        
        # Then start tournament
        response = client.post(f'/tournaments/{sample_tournament.id}/start', 
                             follow_redirects=True)
        assert response.status_code == 200
        
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            assert tournament.status == TournamentStatus.IN_PROGRESS
