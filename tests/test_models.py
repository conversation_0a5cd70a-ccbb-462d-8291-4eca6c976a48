"""Test models and database functionality."""
import pytest
from datetime import datetime, timedelta
from app.models import User, Tournament, Team, Player, Match, UserRole, TournamentStatus, TournamentFormat, MatchStatus
from app import db


class TestUserModel:
    """Test User model functionality."""
    
    def test_user_creation(self, app):
        """Test user creation and basic properties."""
        with app.app_context():
            user = User(
                username='testuser',
                email='<EMAIL>',
                first_name='Test',
                last_name='User',
                role=UserRole.PLAYER
            )
            user.set_password('password123')
            
            assert user.username == 'testuser'
            assert user.email == '<EMAIL>'
            assert user.role == UserRole.PLAYER
            assert user.check_password('password123')
            assert not user.check_password('wrongpassword')
    
    def test_user_repr(self, app):
        """Test user string representation."""
        with app.app_context():
            user = User(username='testuser', email='<EMAIL>')
            assert repr(user) == '<User testuser>'
    
    def test_password_hashing(self, app):
        """Test password hashing functionality."""
        with app.app_context():
            user = User(username='testuser', email='<EMAIL>')
            user.set_password('secret')
            
            assert user.password_hash is not None
            assert user.password_hash != 'secret'
            assert user.check_password('secret')
            assert not user.check_password('wrong')


class TestTournamentModel:
    """Test Tournament model functionality."""
    
    def test_tournament_creation(self, app, organizer_user):
        """Test tournament creation."""
        with app.app_context():
            # Get fresh organizer user
            organizer = User.query.filter_by(username='organizer').first()

            tournament = Tournament(
                name='Test Tournament',
                description='A test tournament',
                game='valorant',
                format=TournamentFormat.SINGLE_ELIMINATION,
                max_teams=16,
                registration_start=datetime.utcnow() + timedelta(days=1),
                registration_end=datetime.utcnow() + timedelta(days=3),
                tournament_start=datetime.utcnow() + timedelta(days=7),
                organizer_id=organizer.id,
                status=TournamentStatus.DRAFT
            )

            assert tournament.name == 'Test Tournament'
            assert tournament.game == 'valorant'
            assert tournament.format == TournamentFormat.SINGLE_ELIMINATION
            assert tournament.max_teams == 16
            assert tournament.status == TournamentStatus.DRAFT
    
    def test_tournament_repr(self, app, organizer_user):
        """Test tournament string representation."""
        with app.app_context():
            # Get fresh organizer user
            organizer = User.query.filter_by(username='organizer').first()

            tournament = Tournament(
                name='Test Tournament',
                game='valorant',
                registration_start=datetime.utcnow() + timedelta(days=1),
                registration_end=datetime.utcnow() + timedelta(days=3),
                tournament_start=datetime.utcnow() + timedelta(days=7),
                organizer_id=organizer.id
            )
            assert repr(tournament) == '<Tournament Test Tournament>'
    
    def test_tournament_relationships(self, app, sample_tournament, sample_teams):
        """Test tournament relationships."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            
            # Test teams relationship
            assert len(tournament.teams) == 4
            assert all(team.tournament_id == tournament.id for team in tournament.teams)
    
    def test_tournament_status_transitions(self, app, sample_tournament):
        """Test tournament status transitions."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            
            # Test status changes
            tournament.status = TournamentStatus.REGISTRATION_CLOSED
            db.session.commit()
            
            updated_tournament = Tournament.query.get(sample_tournament.id)
            assert updated_tournament.status == TournamentStatus.REGISTRATION_CLOSED


class TestTeamModel:
    """Test Team model functionality."""
    
    def test_team_creation(self, app, sample_tournament, player_user):
        """Test team creation."""
        with app.app_context():
            team = Team(
                name='Test Team',
                description='A test team',
                captain_id=player_user.id,
                tournament_id=sample_tournament.id
            )
            
            assert team.name == 'Test Team'
            assert team.captain_id == player_user.id
            assert team.tournament_id == sample_tournament.id
    
    def test_team_repr(self, app, sample_teams):
        """Test team string representation."""
        with app.app_context():
            team = Team.query.first()
            assert 'Team' in repr(team)
    
    def test_team_relationships(self, app, sample_teams, player_user):
        """Test team relationships."""
        with app.app_context():
            team = Team.query.first()
            
            # Test captain relationship
            assert team.captain.id == player_user.id
            
            # Test tournament relationship
            assert team.tournament is not None


class TestPlayerModel:
    """Test Player model functionality."""
    
    def test_player_creation(self, app, player_user):
        """Test player creation."""
        with app.app_context():
            player = Player.query.filter_by(user_id=player_user.id).first()
            
            assert player is not None
            assert player.user_id == player_user.id
            assert player.in_game_name == 'TestPlayer'
    
    def test_player_relationships(self, app, player_user):
        """Test player relationships."""
        with app.app_context():
            player = Player.query.filter_by(user_id=player_user.id).first()
            
            # Test user relationship
            assert player.user.id == player_user.id
            assert player.user.username == 'player'


class TestMatchModel:
    """Test Match model functionality."""
    
    def test_match_creation(self, app, sample_teams):
        """Test match creation."""
        with app.app_context():
            team1, team2 = sample_teams[0], sample_teams[1]
            
            match = Match(
                tournament_id=team1.tournament_id,
                team1_id=team1.id,
                team2_id=team2.id,
                round_number=1,
                match_number=1,
                scheduled_time=datetime.utcnow() + timedelta(hours=24)
            )
            
            assert match.team1_id == team1.id
            assert match.team2_id == team2.id
            assert match.status == MatchStatus.UPCOMING  # Default status
            assert match.round_number == 1
    
    def test_match_result_setting(self, app, sample_teams):
        """Test setting match results."""
        with app.app_context():
            team1, team2 = sample_teams[0], sample_teams[1]
            
            match = Match(
                tournament_id=team1.tournament_id,
                team1_id=team1.id,
                team2_id=team2.id,
                round_number=1,
                match_number=1
            )
            db.session.add(match)
            db.session.commit()
            
            # Set match result
            match.team1_score = 2
            match.team2_score = 1
            match.winner_id = team1.id
            match.status = MatchStatus.COMPLETED
            db.session.commit()
            
            updated_match = Match.query.get(match.id)
            assert updated_match.team1_score == 2
            assert updated_match.team2_score == 1
            assert updated_match.winner_id == team1.id
            assert updated_match.status == MatchStatus.COMPLETED
    
    def test_match_relationships(self, app, sample_teams):
        """Test match relationships."""
        with app.app_context():
            team1, team2 = sample_teams[0], sample_teams[1]
            
            match = Match(
                tournament_id=team1.tournament_id,
                team1_id=team1.id,
                team2_id=team2.id,
                round_number=1,
                match_number=1
            )
            db.session.add(match)
            db.session.commit()
            
            # Test team relationships
            assert match.team1.id == team1.id
            assert match.team2.id == team2.id
            assert match.tournament.id == team1.tournament_id


class TestModelValidation:
    """Test model validation and constraints."""
    
    def test_unique_constraints(self, app):
        """Test unique constraints."""
        with app.app_context():
            # Test unique username
            user1 = User(username='unique', email='<EMAIL>')
            user2 = User(username='unique', email='<EMAIL>')
            
            db.session.add(user1)
            db.session.commit()
            
            db.session.add(user2)
            with pytest.raises(Exception):  # Should raise integrity error
                db.session.commit()
            
            db.session.rollback()
    
    def test_required_fields(self, app):
        """Test required field validation."""
        with app.app_context():
            # Test user without required fields
            user = User()
            db.session.add(user)
            
            with pytest.raises(Exception):  # Should raise validation error
                db.session.commit()
            
            db.session.rollback()
    
    def test_enum_validation(self, app):
        """Test enum field validation."""
        with app.app_context():
            user = User(
                username='testuser',
                email='<EMAIL>',
                role=UserRole.PLAYER
            )
            
            # Valid enum value
            assert user.role == UserRole.PLAYER
            
            # Test that enum values are properly constrained
            valid_roles = [UserRole.PLAYER, UserRole.ORGANIZER, UserRole.ADMIN]
            assert user.role in valid_roles
