"""Test teams functionality."""
import pytest
from flask import url_for
from app.models import User, UserR<PERSON>, Team, Player, Tournament, TournamentTeam, Match, MatchStatus, TournamentStatus
from app.services.team_service import TeamService
from app import db
from tests.conftest import login_user, logout_user
from datetime import datetime, timedelta


class TestTeamRoutes:
    """Test team routes."""
    
    def test_teams_index_loads(self, client):
        """Test teams index page loads correctly."""
        response = client.get('/teams/')
        assert response.status_code == 200
        assert b'Teams' in response.data

    def test_teams_index_with_search(self, client, app):
        """Test teams index with search functionality."""
        with app.app_context():
            # Create test teams
            team1 = Team(name='Alpha Team', tag='ALPHA', description='First team')
            team2 = Team(name='Beta Squad', tag='BETA', description='Second team')
            team3 = Team(name='Gamma Force', tag='GAMMA', description='Third team')

            db.session.add_all([team1, team2, team3])
            db.session.commit()

            # Test search by name
            response = client.get('/teams/?search=Alpha')
            assert response.status_code == 200
            assert b'Alpha Team' in response.data
            assert b'Beta Squad' not in response.data

            # Test search by tag
            response = client.get('/teams/?search=BETA')
            assert response.status_code == 200
            assert b'Beta Squad' in response.data
            assert b'Alpha Team' not in response.data

            # Test search by description
            response = client.get('/teams/?search=Third')
            assert response.status_code == 200
            assert b'Gamma Force' in response.data
            assert b'Alpha Team' not in response.data

    def test_teams_index_with_filters(self, client, app):
        """Test teams index with filtering options."""
        with app.app_context():
            # Create users and players
            user1 = User(username='captain1', email='<EMAIL>',
                        first_name='Captain', last_name='One', role=UserRole.PLAYER)
            user1.set_password('password')
            db.session.add(user1)
            db.session.commit()

            player1 = Player(user_id=user1.id, in_game_name='Captain1')
            db.session.add(player1)
            db.session.commit()

            # Create teams with different characteristics
            team_with_captain = Team(name='Team With Captain', tag='TWC', captain_id=player1.id)
            team_without_captain = Team(name='Team Without Captain', tag='TWOC')

            db.session.add_all([team_with_captain, team_without_captain])
            db.session.commit()

            # Test captain filter
            response = client.get('/teams/?has_captain=yes')
            assert response.status_code == 200
            assert b'Team With Captain' in response.data

            response = client.get('/teams/?has_captain=no')
            assert response.status_code == 200
            assert b'Team Without Captain' in response.data

    def test_teams_index_sorting(self, client, app):
        """Test teams index sorting functionality."""
        with app.app_context():
            # Create teams with different creation dates
            team1 = Team(name='Zebra Team', tag='ZEB')
            team2 = Team(name='Alpha Team', tag='ALP')

            db.session.add(team1)
            db.session.commit()

            # Add second team with slight delay to ensure different timestamps
            team2.created_at = datetime.utcnow() + timedelta(seconds=1)
            db.session.add(team2)
            db.session.commit()

            # Test name sorting ascending
            response = client.get('/teams/?sort=name&order=asc')
            assert response.status_code == 200
            content = response.data.decode()
            alpha_pos = content.find('Alpha Team')
            zebra_pos = content.find('Zebra Team')
            assert alpha_pos < zebra_pos

            # Test name sorting descending
            response = client.get('/teams/?sort=name&order=desc')
            assert response.status_code == 200
            content = response.data.decode()
            alpha_pos = content.find('Alpha Team')
            zebra_pos = content.find('Zebra Team')
            assert zebra_pos < alpha_pos

    def test_teams_index_pagination(self, client, app):
        """Test teams index pagination."""
        with app.app_context():
            # Create many teams to test pagination
            teams = []
            for i in range(15):
                team = Team(name=f'Team {i:02d}', tag=f'T{i:02d}')
                teams.append(team)

            db.session.add_all(teams)
            db.session.commit()

            # Test first page
            response = client.get('/teams/')
            assert response.status_code == 200
            assert b'Team 00' in response.data

            # Test second page
            response = client.get('/teams/?page=2')
            assert response.status_code == 200
            # Should have remaining teams on second page
    
    def test_my_teams_requires_login(self, client):
        """Test my_teams route requires authentication."""
        response = client.get('/teams/my-teams')
        assert response.status_code == 302  # Redirect to login
    
    def test_my_teams_url_for_works(self, app):
        """Test that url_for('teams.my_teams') works without BuildError."""
        with app.app_context():
            url = url_for('teams.my_teams')
            assert url == '/teams/my-teams'
    
    def test_my_teams_with_player_user(self, client, app, player_user):
        """Test my_teams route with authenticated player user."""
        # Login as player
        login_user(client, 'player', 'password')
        
        # Access my_teams route
        response = client.get('/teams/my-teams')
        
        # Should redirect to profile if no player profile exists
        # or show the my_teams page if player profile exists
        assert response.status_code in [200, 302]
    
    def test_my_teams_with_non_player_user(self, client, app, organizer_user):
        """Test my_teams route with non-player user."""
        # Login as organizer
        login_user(client, 'organizer', 'password')
        
        # Access my_teams route
        response = client.get('/teams/my-teams', follow_redirects=True)
        
        # Should redirect with error message
        assert response.status_code == 200
        assert b'Only players can view team memberships' in response.data or b'Team list' in response.data

    def test_team_detail_view(self, client, app):
        """Test team detail view."""
        with app.app_context():
            # Create a team
            team = Team(name='Test Team', tag='TEST', description='A test team')
            db.session.add(team)
            db.session.commit()

            # Test team detail view
            response = client.get(f'/teams/{team.id}')
            assert response.status_code == 200
            assert b'Test Team' in response.data
            assert b'TEST' in response.data
            assert b'A test team' in response.data

    def test_team_detail_view_inactive_team(self, client, app):
        """Test team detail view for inactive team."""
        with app.app_context():
            # Create an inactive team
            team = Team(name='Inactive Team', tag='INACT', is_active=False)
            db.session.add(team)
            db.session.commit()

            # Test team detail view redirects
            response = client.get(f'/teams/{team.id}', follow_redirects=True)
            assert response.status_code == 200
            assert b'no longer active' in response.data or b'Teams' in response.data

    def test_team_detail_view_nonexistent_team(self, client):
        """Test team detail view for nonexistent team."""
        response = client.get('/teams/99999')
        assert response.status_code == 404


class TestTeamModels:
    """Test team model functionality."""
    
    def test_team_creation(self, app):
        """Test team creation."""
        with app.app_context():
            # Create a user and player first
            user = User(
                username='testcaptain',
                email='<EMAIL>',
                first_name='Test',
                last_name='Captain',
                role=UserRole.PLAYER
            )
            user.set_password('password')
            db.session.add(user)
            db.session.commit()
            
            # Create player profile
            player = Player(
                user_id=user.id,
                in_game_name='TestCaptain'
            )
            db.session.add(player)
            db.session.commit()
            
            # Create team
            team = Team(
                name='Test Team',
                tag='TEST',
                description='A test team',
                captain_id=player.id
            )
            db.session.add(team)
            db.session.commit()
            
            assert team.name == 'Test Team'
            assert team.tag == 'TEST'
            assert team.captain_id == player.id
            assert team.is_active is True


class TestTeamService:
    """Test TeamService functionality."""

    def test_get_team_statistics_empty_team(self, app):
        """Test getting statistics for a team with no matches."""
        with app.app_context():
            team = Team(name='Empty Team', tag='EMPTY')
            db.session.add(team)
            db.session.commit()

            stats = TeamService.get_team_statistics(team)

            assert stats['total_tournaments'] == 0
            assert stats['total_matches'] == 0
            assert stats['total_wins'] == 0
            assert stats['total_losses'] == 0
            assert stats['win_rate'] == 0.0
            assert stats['recent_matches'] == []

    def test_get_team_statistics_with_matches(self, app):
        """Test getting statistics for a team with matches."""
        with app.app_context():
            # Create users and players
            user1 = User(username='captain1', email='<EMAIL>',
                        first_name='Captain', last_name='One', role=UserRole.PLAYER)
            user1.set_password('password')
            user2 = User(username='organizer1', email='<EMAIL>',
                        first_name='Organizer', last_name='One', role=UserRole.ORGANIZER)
            user2.set_password('password')
            db.session.add_all([user1, user2])
            db.session.commit()

            player1 = Player(user_id=user1.id, in_game_name='Captain1')
            db.session.add(player1)
            db.session.commit()

            # Create teams
            team1 = Team(name='Team One', tag='T1', captain_id=player1.id)
            team2 = Team(name='Team Two', tag='T2')
            db.session.add_all([team1, team2])
            db.session.commit()

            # Create tournament
            tournament = Tournament(
                name='Test Tournament',
                game='Test Game',
                organizer_id=user2.id,
                registration_start=datetime.utcnow() - timedelta(days=10),
                registration_end=datetime.utcnow() - timedelta(days=5),
                tournament_start=datetime.utcnow() - timedelta(days=3),
                status=TournamentStatus.IN_PROGRESS
            )
            db.session.add(tournament)
            db.session.commit()

            # Create tournament registrations
            reg1 = TournamentTeam(tournament_id=tournament.id, team_id=team1.id)
            reg2 = TournamentTeam(tournament_id=tournament.id, team_id=team2.id)
            db.session.add_all([reg1, reg2])
            db.session.commit()

            # Create a match
            match = Match(
                tournament_id=tournament.id,
                round_number=1,
                match_number=1,
                team1_id=reg1.id,
                team2_id=reg2.id,
                winner_id=reg1.id,
                status=MatchStatus.COMPLETED,
                team1_score=2,
                team2_score=1,
                completed_at=datetime.utcnow()
            )
            db.session.add(match)
            db.session.commit()

            # Test statistics for winning team
            stats = TeamService.get_team_statistics(team1)

            assert stats['total_tournaments'] == 1
            assert stats['total_matches'] == 1
            assert stats['total_wins'] == 1
            assert stats['total_losses'] == 0
            assert stats['win_rate'] == 100.0
            assert len(stats['recent_matches']) == 1

    def test_search_teams(self, app):
        """Test team search functionality."""
        with app.app_context():
            # Create test teams
            team1 = Team(name='Alpha Warriors', tag='AW', description='Elite team')
            team2 = Team(name='Beta Squad', tag='BS', description='Casual players')
            team3 = Team(name='Gamma Force', tag='GF', description='Professional esports')

            db.session.add_all([team1, team2, team3])
            db.session.commit()

            # Test search by name
            results = TeamService.search_teams('Alpha')
            assert len(results) == 1
            assert results[0].name == 'Alpha Warriors'

            # Test search by tag
            results = TeamService.search_teams('BS')
            assert len(results) == 1
            assert results[0].name == 'Beta Squad'

            # Test search by description
            results = TeamService.search_teams('Professional')
            assert len(results) == 1
            assert results[0].name == 'Gamma Force'

            # Test empty search
            results = TeamService.search_teams('')
            assert len(results) == 0

    def test_get_team_members_with_stats(self, app):
        """Test getting team members with statistics."""
        with app.app_context():
            # Create users and players
            user1 = User(username='captain1', email='<EMAIL>',
                        first_name='Captain', last_name='One', role=UserRole.PLAYER)
            user2 = User(username='member1', email='<EMAIL>',
                        first_name='Member', last_name='One', role=UserRole.PLAYER)
            user1.set_password('password')
            user2.set_password('password')
            db.session.add_all([user1, user2])
            db.session.commit()

            player1 = Player(user_id=user1.id, in_game_name='Captain1', matches_played=10, matches_won=7)
            player2 = Player(user_id=user2.id, in_game_name='Member1', matches_played=5, matches_won=3)
            db.session.add_all([player1, player2])
            db.session.commit()

            # Create team
            team = Team(name='Test Team', tag='TEST', captain_id=player1.id)
            db.session.add(team)
            db.session.commit()

            # Assign players to team
            player1.team_id = team.id
            player2.team_id = team.id
            db.session.commit()

            # Get members with stats
            members = TeamService.get_team_members_with_stats(team)

            assert len(members) == 2
            # Captain should be first
            assert members[0]['is_captain'] is True
            assert members[0]['in_game_name'] == 'Captain1'
            assert members[1]['is_captain'] is False
            assert members[1]['in_game_name'] == 'Member1'
    
    def test_team_captain_relationship(self, app):
        """Test team captain relationship."""
        with app.app_context():
            # Create user and player
            user = User(
                username='testcaptain2',
                email='<EMAIL>',
                first_name='Test',
                last_name='Captain2',
                role=UserRole.PLAYER
            )
            user.set_password('password')
            db.session.add(user)
            db.session.commit()
            
            player = Player(
                user_id=user.id,
                in_game_name='TestCaptain2'
            )
            db.session.add(player)
            db.session.commit()
            
            # Create team
            team = Team(
                name='Test Team 2',
                tag='TEST2',
                captain_id=player.id
            )
            db.session.add(team)
            db.session.commit()
            
            # Test relationships
            assert team.captain == player
            assert team.captain.user == user
