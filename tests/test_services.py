"""Test service layer functionality."""
import pytest
from datetime import datetime, timedelta
from app.models import Tournament, Team, Match, TournamentFormat, MatchStatus, TournamentTeam, User, Player
from app import db


class TestBracketGenerator:
    """Test bracket generation service."""

    def test_tournament_teams_relationship(self, app, sample_tournament, sample_teams):
        """Test that tournament teams relationship works correctly."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)

            # Check that teams are properly associated with tournament
            tournament_teams = tournament.teams.all()
            assert len(tournament_teams) == 4

            # Check that each TournamentTeam has a valid team
            for tournament_team in tournament_teams:
                assert tournament_team.team is not None
                assert tournament_team.team.name.startswith('Team')

    def test_basic_bracket_logic(self, app, sample_tournament, sample_teams):
        """Test basic bracket generation logic without complex services."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            tournament.format = TournamentFormat.SINGLE_ELIMINATION
            db.session.commit()

            # Get teams registered for this tournament
            tournament_teams = tournament.teams.all()
            teams = [tt.team for tt in tournament_teams]

            # Basic bracket logic: 4 teams need 3 matches (2 + 1)
            assert len(teams) == 4

            # Create matches manually for testing
            matches = []

            # Round 1: 2 matches
            match1 = Match(
                tournament_id=tournament.id,
                team1_id=teams[0].id,
                team2_id=teams[1].id,
                round_number=1,
                match_number=1,
                status=MatchStatus.SCHEDULED
            )
            match2 = Match(
                tournament_id=tournament.id,
                team1_id=teams[2].id,
                team2_id=teams[3].id,
                round_number=1,
                match_number=2,
                status=MatchStatus.SCHEDULED
            )

            # Round 2: 1 match (final)
            match3 = Match(
                tournament_id=tournament.id,
                round_number=2,
                match_number=1,
                status=MatchStatus.SCHEDULED
            )

            matches = [match1, match2, match3]

            # Verify structure
            round_1_matches = [m for m in matches if m.round_number == 1]
            round_2_matches = [m for m in matches if m.round_number == 2]

            assert len(round_1_matches) == 2
            assert len(round_2_matches) == 1
    
    def test_single_elimination_bracket_8_teams(self, app, organizer_user):
        """Test single elimination bracket generation with 8 teams."""
        with app.app_context():
            # Create tournament with 8 teams
            tournament = Tournament(
                name='8 Team Tournament',
                description='Tournament with 8 teams',
                game='valorant',
                format=TournamentFormat.SINGLE_ELIMINATION,
                max_teams=8,
                start_date=datetime.utcnow() + timedelta(days=7),
                registration_deadline=datetime.utcnow() + timedelta(days=3),
                organizer_id=organizer_user.id
            )
            db.session.add(tournament)
            db.session.commit()
            
            # Create 8 teams
            teams = []
            for i in range(8):
                team = Team(
                    name=f'Team {i+1}',
                    description=f'Test team {i+1}',
                    captain_id=organizer_user.id,
                    tournament_id=tournament.id
                )
                db.session.add(team)
                teams.append(team)
            db.session.commit()
            
            generator = BracketGenerator()
            matches = generator.generate_bracket(tournament)
            
            # 8 teams should create 7 matches (4 + 2 + 1)
            assert len(matches) == 7
            
            # Check round structure
            round_1_matches = [m for m in matches if m.round_number == 1]
            round_2_matches = [m for m in matches if m.round_number == 2]
            round_3_matches = [m for m in matches if m.round_number == 3]
            
            assert len(round_1_matches) == 4  # Quarterfinals
            assert len(round_2_matches) == 2  # Semifinals
            assert len(round_3_matches) == 1  # Final
    
    def test_round_robin_bracket(self, app, sample_tournament, sample_teams):
        """Test round robin bracket generation."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            tournament.format = TournamentFormat.ROUND_ROBIN
            db.session.commit()
            
            generator = BracketGenerator()
            matches = generator.generate_bracket(tournament)
            
            # 4 teams in round robin should create 6 matches (each team plays each other once)
            # Formula: n * (n-1) / 2 = 4 * 3 / 2 = 6
            assert len(matches) == 6
            
            # All matches should be in round 1 for round robin
            assert all(match.round_number == 1 for match in matches)
            
            # Check that each team plays each other team exactly once
            team_pairs = set()
            for match in matches:
                pair = tuple(sorted([match.team1_id, match.team2_id]))
                assert pair not in team_pairs  # No duplicate pairings
                team_pairs.add(pair)
    
    def test_bracket_generation_insufficient_teams(self, app, organizer_user):
        """Test bracket generation with insufficient teams."""
        with app.app_context():
            # Create tournament with only 1 team
            tournament = Tournament(
                name='Single Team Tournament',
                description='Tournament with insufficient teams',
                game='valorant',
                format=TournamentFormat.SINGLE_ELIMINATION,
                max_teams=8,
                start_date=datetime.utcnow() + timedelta(days=7),
                registration_deadline=datetime.utcnow() + timedelta(days=3),
                organizer_id=organizer_user.id
            )
            db.session.add(tournament)
            db.session.commit()
            
            # Create only 1 team
            team = Team(
                name='Lonely Team',
                description='Only team',
                captain_id=organizer_user.id,
                tournament_id=tournament.id
            )
            db.session.add(team)
            db.session.commit()
            
            generator = BracketGenerator()
            
            with pytest.raises(ValueError, match="at least 2 teams"):
                generator.generate_bracket(tournament)


class TestMatchScheduler:
    """Test match scheduling service."""
    
    def test_schedule_matches_basic(self, app, sample_tournament, sample_teams):
        """Test basic match scheduling."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            
            # Generate bracket first
            generator = BracketGenerator()
            matches = generator.generate_bracket(tournament)
            
            # Schedule matches
            scheduler = MatchScheduler()
            scheduled_matches = scheduler.schedule_matches(tournament, matches)
            
            assert len(scheduled_matches) == len(matches)
            
            # Check that all matches have scheduled times
            for match in scheduled_matches:
                assert match.scheduled_time is not None
                assert match.scheduled_time > datetime.utcnow()
    
    def test_schedule_matches_with_intervals(self, app, sample_tournament, sample_teams):
        """Test match scheduling with proper intervals."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            
            generator = BracketGenerator()
            matches = generator.generate_bracket(tournament)
            
            scheduler = MatchScheduler()
            scheduled_matches = scheduler.schedule_matches(
                tournament, matches, match_duration_minutes=60, break_minutes=30
            )
            
            # Sort matches by scheduled time
            scheduled_matches.sort(key=lambda m: m.scheduled_time)
            
            # Check intervals between matches in the same round
            round_1_matches = [m for m in scheduled_matches if m.round_number == 1]
            if len(round_1_matches) > 1:
                for i in range(1, len(round_1_matches)):
                    time_diff = round_1_matches[i].scheduled_time - round_1_matches[i-1].scheduled_time
                    # Should be at least match duration + break time
                    assert time_diff >= timedelta(minutes=90)  # 60 + 30
    
    def test_schedule_matches_different_rounds(self, app, sample_tournament, sample_teams):
        """Test scheduling matches across different rounds."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            tournament.format = TournamentFormat.SINGLE_ELIMINATION
            db.session.commit()
            
            generator = BracketGenerator()
            matches = generator.generate_bracket(tournament)
            
            scheduler = MatchScheduler()
            scheduled_matches = scheduler.schedule_matches(tournament, matches)
            
            # Group matches by round
            rounds = {}
            for match in scheduled_matches:
                if match.round_number not in rounds:
                    rounds[match.round_number] = []
                rounds[match.round_number].append(match)
            
            # Check that later rounds are scheduled after earlier rounds
            if len(rounds) > 1:
                round_1_latest = max(rounds[1], key=lambda m: m.scheduled_time).scheduled_time
                round_2_earliest = min(rounds[2], key=lambda m: m.scheduled_time).scheduled_time
                
                assert round_2_earliest > round_1_latest


class TestLeaderboardService:
    """Test leaderboard service."""
    
    def test_calculate_single_elimination_leaderboard(self, app, sample_tournament, sample_teams):
        """Test leaderboard calculation for single elimination."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            tournament.format = TournamentFormat.SINGLE_ELIMINATION
            db.session.commit()
            
            # Create some completed matches
            team1, team2, team3, team4 = sample_teams
            
            # Semifinal 1: Team1 beats Team2
            match1 = Match(
                tournament_id=tournament.id,
                team1_id=team1.id,
                team2_id=team2.id,
                round_number=1,
                match_number=1,
                team1_score=2,
                team2_score=1,
                winner_id=team1.id,
                status=MatchStatus.COMPLETED
            )
            
            # Semifinal 2: Team3 beats Team4
            match2 = Match(
                tournament_id=tournament.id,
                team1_id=team3.id,
                team2_id=team4.id,
                round_number=1,
                match_number=2,
                team1_score=2,
                team2_score=0,
                winner_id=team3.id,
                status=MatchStatus.COMPLETED
            )
            
            # Final: Team1 beats Team3
            match3 = Match(
                tournament_id=tournament.id,
                team1_id=team1.id,
                team2_id=team3.id,
                round_number=2,
                match_number=1,
                team1_score=2,
                team2_score=1,
                winner_id=team1.id,
                status=MatchStatus.COMPLETED
            )
            
            db.session.add_all([match1, match2, match3])
            db.session.commit()
            
            service = LeaderboardService()
            leaderboard = service.calculate_leaderboard(tournament)
            
            # Check leaderboard order
            assert len(leaderboard) == 4
            assert leaderboard[0]['team_id'] == team1.id  # Winner
            assert leaderboard[1]['team_id'] == team3.id  # Runner-up
            # Team2 and Team4 should be tied for 3rd place
    
    def test_calculate_round_robin_leaderboard(self, app, sample_tournament, sample_teams):
        """Test leaderboard calculation for round robin."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            tournament.format = TournamentFormat.ROUND_ROBIN
            db.session.commit()
            
            team1, team2, team3, team4 = sample_teams
            
            # Create round robin matches with results
            matches = [
                # Team1 vs Team2: Team1 wins
                Match(tournament_id=tournament.id, team1_id=team1.id, team2_id=team2.id,
                     round_number=1, match_number=1, team1_score=2, team2_score=1,
                     winner_id=team1.id, status=MatchStatus.COMPLETED),
                
                # Team1 vs Team3: Team1 wins
                Match(tournament_id=tournament.id, team1_id=team1.id, team2_id=team3.id,
                     round_number=1, match_number=2, team1_score=2, team2_score=0,
                     winner_id=team1.id, status=MatchStatus.COMPLETED),
                
                # Team1 vs Team4: Team4 wins
                Match(tournament_id=tournament.id, team1_id=team1.id, team2_id=team4.id,
                     round_number=1, match_number=3, team1_score=1, team2_score=2,
                     winner_id=team4.id, status=MatchStatus.COMPLETED),
                
                # Team2 vs Team3: Team2 wins
                Match(tournament_id=tournament.id, team1_id=team2.id, team2_id=team3.id,
                     round_number=1, match_number=4, team1_score=2, team2_score=1,
                     winner_id=team2.id, status=MatchStatus.COMPLETED),
                
                # Team2 vs Team4: Team4 wins
                Match(tournament_id=tournament.id, team1_id=team2.id, team2_id=team4.id,
                     round_number=1, match_number=5, team1_score=0, team2_score=2,
                     winner_id=team4.id, status=MatchStatus.COMPLETED),
                
                # Team3 vs Team4: Team4 wins
                Match(tournament_id=tournament.id, team1_id=team3.id, team2_id=team4.id,
                     round_number=1, match_number=6, team1_score=1, team2_score=2,
                     winner_id=team4.id, status=MatchStatus.COMPLETED)
            ]
            
            db.session.add_all(matches)
            db.session.commit()
            
            service = LeaderboardService()
            leaderboard = service.calculate_leaderboard(tournament)
            
            # Team4 should be first (3 wins)
            # Team1 should be second (2 wins)
            # Team2 should be third (1 win)
            # Team3 should be fourth (0 wins)
            
            assert len(leaderboard) == 4
            assert leaderboard[0]['team_id'] == team4.id
            assert leaderboard[0]['wins'] == 3
            assert leaderboard[1]['team_id'] == team1.id
            assert leaderboard[1]['wins'] == 2
    
    def test_leaderboard_with_no_matches(self, app, sample_tournament, sample_teams):
        """Test leaderboard calculation with no completed matches."""
        with app.app_context():
            tournament = Tournament.query.get(sample_tournament.id)
            
            service = LeaderboardService()
            leaderboard = service.calculate_leaderboard(tournament)
            
            # Should return all teams with zero stats
            assert len(leaderboard) == 4
            for entry in leaderboard:
                assert entry['wins'] == 0
                assert entry['losses'] == 0
                assert entry['points'] == 0
