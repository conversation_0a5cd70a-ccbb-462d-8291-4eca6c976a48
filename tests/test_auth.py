"""Test authentication functionality."""
import pytest
from flask import url_for
from app.models import User, User<PERSON>ole
from app import db
from tests.conftest import login_user, logout_user


class TestAuthRoutes:
    """Test authentication routes."""
    
    def test_register_page_loads(self, client):
        """Test registration page loads correctly."""
        response = client.get('/auth/register')
        assert response.status_code == 200
        assert b'Create Account' in response.data
    
    def test_login_page_loads(self, client):
        """Test login page loads correctly."""
        response = client.get('/auth/login')
        assert response.status_code == 200
        assert b'Sign In' in response.data
    
    def test_user_registration(self, client, app):
        """Test user registration process."""
        response = client.post('/auth/register', data={
            'username': 'newuser',
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'password123',
            'password2': 'password123',
            'role': 'player'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        
        # Check user was created in database
        with app.app_context():
            user = User.query.filter_by(username='newuser').first()
            assert user is not None
            assert user.email == '<EMAIL>'
            assert user.role == UserRole.PLAYER
    
    def test_user_login(self, client, organizer_user):
        """Test user login process."""
        response = login_user(client, 'organizer', 'password')
        assert response.status_code == 200
        
        # Check that user is redirected to home page after login
        assert b'Tournaments' in response.data or b'Welcome' in response.data
    
    def test_user_logout(self, client, organizer_user):
        """Test user logout process."""
        # First login
        login_user(client, 'organizer', 'password')
        
        # Then logout
        response = logout_user(client)
        assert response.status_code == 200
        
        # Check that user is logged out
        response = client.get('/tournaments/create')
        assert response.status_code == 302  # Should redirect to login
    
    def test_invalid_login(self, client, organizer_user):
        """Test login with invalid credentials."""
        response = client.post('/auth/login', data={
            'username': 'organizer',
            'password': 'wrongpassword'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert b'Invalid username or password' in response.data
    
    def test_registration_validation(self, client):
        """Test registration form validation."""
        # Test password mismatch
        response = client.post('/auth/register', data={
            'username': 'testuser',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'password123',
            'password2': 'differentpassword',
            'role': 'player'
        })
        
        assert response.status_code == 200
        assert b'Field must be equal to password' in response.data
    
    def test_duplicate_username_registration(self, client, organizer_user):
        """Test registration with duplicate username."""
        response = client.post('/auth/register', data={
            'username': 'organizer',  # Already exists
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'password123',
            'password2': 'password123',
            'role': 'player'
        })
        
        assert response.status_code == 200
        assert b'Username already exists' in response.data or b'already in use' in response.data


class TestAuthProtection:
    """Test authentication protection on routes."""
    
    def test_protected_routes_redirect(self, client):
        """Test that protected routes redirect to login."""
        protected_routes = [
            '/tournaments/create',
            '/admin/dashboard',
            '/tournaments/my-tournaments'
        ]
        
        for route in protected_routes:
            response = client.get(route)
            assert response.status_code == 302
            assert '/auth/login' in response.location
    
    def test_admin_routes_require_admin(self, client, organizer_user):
        """Test that admin routes require admin role."""
        # Login as organizer (not admin)
        login_user(client, 'organizer', 'password')
        
        response = client.get('/admin/dashboard')
        assert response.status_code == 302  # Should redirect
    
    def test_organizer_routes_require_organizer(self, client, player_user):
        """Test that organizer routes require organizer role."""
        # Login as player (not organizer)
        login_user(client, 'player', 'password')
        
        response = client.get('/tournaments/create')
        assert response.status_code == 302  # Should redirect with error


class TestUserRoles:
    """Test user role functionality."""
    
    def test_admin_access(self, client, admin_user):
        """Test admin user access."""
        login_user(client, 'admin', 'password')
        
        # Admin should access admin dashboard
        response = client.get('/admin/dashboard')
        assert response.status_code == 200
        
        # Admin should access tournament creation
        response = client.get('/tournaments/create')
        assert response.status_code == 200
    
    def test_organizer_access(self, client, organizer_user):
        """Test organizer user access."""
        login_user(client, 'organizer', 'password')
        
        # Organizer should access tournament creation
        response = client.get('/tournaments/create')
        assert response.status_code == 200
        
        # Organizer should NOT access admin dashboard
        response = client.get('/admin/dashboard')
        assert response.status_code == 302
    
    def test_player_access(self, client, player_user):
        """Test player user access."""
        login_user(client, 'player', 'password')
        
        # Player should NOT access tournament creation
        response = client.get('/tournaments/create')
        assert response.status_code == 302
        
        # Player should NOT access admin dashboard
        response = client.get('/admin/dashboard')
        assert response.status_code == 302
        
        # Player should access public pages
        response = client.get('/tournaments/')
        assert response.status_code == 200


class TestPasswordSecurity:
    """Test password security features."""
    
    def test_password_hashing(self, app):
        """Test that passwords are properly hashed."""
        with app.app_context():
            user = User(username='testuser', email='<EMAIL>')
            user.set_password('plaintext')
            
            # Password should be hashed, not stored as plaintext
            assert user.password_hash != 'plaintext'
            assert len(user.password_hash) > 20  # Hashed passwords are longer
            
            # Should be able to verify correct password
            assert user.check_password('plaintext')
            assert not user.check_password('wrong')
    
    def test_password_requirements(self, client):
        """Test password requirements in registration."""
        # Test short password
        response = client.post('/auth/register', data={
            'username': 'testuser',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'password': '123',  # Too short
            'password2': '123',
            'role': 'player'
        })
        
        assert response.status_code == 200
        # Should show validation error for short password


class TestSessionManagement:
    """Test session management."""
    
    def test_session_persistence(self, client, organizer_user):
        """Test that user session persists across requests."""
        # Login
        login_user(client, 'organizer', 'password')
        
        # Make authenticated request
        response = client.get('/tournaments/create')
        assert response.status_code == 200
        
        # Make another authenticated request
        response = client.get('/tournaments/')
        assert response.status_code == 200
    
    def test_logout_clears_session(self, client, organizer_user):
        """Test that logout properly clears session."""
        # Login
        login_user(client, 'organizer', 'password')
        
        # Verify authenticated access
        response = client.get('/tournaments/create')
        assert response.status_code == 200
        
        # Logout
        logout_user(client)
        
        # Verify access is denied after logout
        response = client.get('/tournaments/create')
        assert response.status_code == 302
